using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 整合像素渲染系統測試
    /// 測試PixelPerfectManager + PixelDrawEngine + PixelCanvas的完整流程
    /// </summary>
    public class IntegratedPixelRenderingTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private int testCanvasSize = 64;
        [SerializeField] private bool autoRunTests = true;
        [SerializeField] private bool showVisualResults = true;
        
        [Header("Test Results")]
        [SerializeField] private GameObject testResultParent;
        
        private PixelPerfectManager pixelPerfectManager;
        private PixelDrawEngine drawEngine;
        private PixelCanvas testCanvas;
        
        // Test result tracking
        private int testsRun = 0;
        private int testsPassed = 0;
        private int testsFailed = 0;
        
        private void Start()
        {
            InitializeTestEnvironment();
            
            if (autoRunTests)
            {
                RunAllTests();
            }
        }
        
        private void InitializeTestEnvironment()
        {
            Debug.Log("=== Initializing Integrated Pixel Rendering Test ===");
            
            // 確保有PixelPerfectManager
            pixelPerfectManager = PixelPerfectManager.Instance;
            if (pixelPerfectManager == null)
            {
                GameObject managerObj = new GameObject("PixelPerfectManager");
                pixelPerfectManager = managerObj.AddComponent<PixelPerfectManager>();
                Debug.Log("✓ Created PixelPerfectManager");
            }
            else
            {
                Debug.Log("✓ Found existing PixelPerfectManager");
            }
            
            // 獲取PixelDrawEngine實例
            drawEngine = PixelDrawEngine.Instance;
            Debug.Log("✓ PixelDrawEngine instance obtained");
            
            // 創建測試結果父物件
            if (testResultParent == null)
            {
                testResultParent = new GameObject("Test Results");
                testResultParent.transform.position = Vector3.zero;
            }
            
            Debug.Log("✓ Test environment initialized");
        }
        
        public void RunAllTests()
        {
            Debug.Log("=== Starting Integrated Pixel Rendering Tests ===");
            
            testsRun = 0;
            testsPassed = 0;
            testsFailed = 0;
            
            // 清理之前的測試結果
            ClearPreviousResults();
            
            // 運行測試
            TestPixelPerfectIntegration();
            TestResolutionAdaptiveRendering();
            TestComplexPixelArt();
            TestPerformanceWithMultipleCanvases();
            TestMemoryManagement();
            
            // 顯示測試結果
            DisplayTestSummary();
        }
        
        private void TestPixelPerfectIntegration()
        {
            StartTest("Pixel Perfect Integration");
            
            try
            {
                // 創建測試畫布
                testCanvas = drawEngine.CreateCanvas(testCanvasSize, testCanvasSize);
                AssertNotNull(testCanvas, "Canvas creation");
                
                // 測試基本像素設置
                testCanvas.SetPixel(10, 10, Color.red);
                Color retrievedColor = testCanvas.GetPixel(10, 10);
                AssertColorEqual(Color.red, retrievedColor, "Basic pixel set/get");
                
                // 測試像素完美渲染設置
                float pixelSize = pixelPerfectManager.GetPixelSize();
                int scaleFactor = pixelPerfectManager.GetScaleFactor();
                
                AssertTrue(pixelSize > 0, "Pixel size calculation");
                AssertTrue(scaleFactor >= 1, "Scale factor calculation");
                
                // 創建並顯示測試精靈
                if (showVisualResults)
                {
                    CreateTestSprite(testCanvas, "PixelPerfect_Test", Vector3.zero);
                }
                
                PassTest();
            }
            catch (System.Exception e)
            {
                FailTest($"Exception: {e.Message}");
            }
            finally
            {
                if (testCanvas != null)
                {
                    drawEngine.ReturnCanvas(testCanvas);
                }
            }
        }
        
        private void TestResolutionAdaptiveRendering()
        {
            StartTest("Resolution Adaptive Rendering");
            
            try
            {
                // 測試不同解析度下的渲染
                Vector2 baseResolution = pixelPerfectManager.GetBaseResolution();
                Vector2 effectiveResolution = pixelPerfectManager.EffectiveResolution;
                
                AssertTrue(baseResolution.x > 0 && baseResolution.y > 0, "Base resolution valid");
                AssertTrue(effectiveResolution.x > 0 && effectiveResolution.y > 0, "Effective resolution valid");
                
                // 測試座標轉換
                Vector3 worldPos = new Vector3(1, 1, 0);
                Vector3 screenPos = pixelPerfectManager.WorldToScreenPoint(worldPos);
                Vector3 backToWorld = pixelPerfectManager.ScreenToWorldPoint(screenPos);
                
                float tolerance = 0.1f;
                AssertTrue(Vector3.Distance(worldPos, backToWorld) < tolerance, "Coordinate conversion accuracy");
                
                PassTest();
            }
            catch (System.Exception e)
            {
                FailTest($"Exception: {e.Message}");
            }
        }
        
        private void TestComplexPixelArt()
        {
            StartTest("Complex Pixel Art Rendering");
            
            PixelCanvas artCanvas = null;
            try
            {
                artCanvas = drawEngine.CreateCanvas(32, 32);
                
                // 繪製複雜圖案
                DrawTestCharacter(artCanvas);
                
                // 驗證繪製結果
                AssertTrue(artCanvas.GetPixel(16, 16) != Color.clear, "Character center drawn");
                
                if (showVisualResults)
                {
                    CreateTestSprite(artCanvas, "ComplexArt_Test", new Vector3(2, 0, 0));
                }
                
                PassTest();
            }
            catch (System.Exception e)
            {
                FailTest($"Exception: {e.Message}");
            }
            finally
            {
                if (artCanvas != null)
                {
                    drawEngine.ReturnCanvas(artCanvas);
                }
            }
        }
        
        private void TestPerformanceWithMultipleCanvases()
        {
            StartTest("Performance with Multiple Canvases");
            
            try
            {
                float startTime = Time.realtimeSinceStartup;
                
                // 創建多個畫布並繪製
                for (int i = 0; i < 10; i++)
                {
                    PixelCanvas canvas = drawEngine.CreateCanvas(16, 16);
                    
                    // 繪製簡單圖案
                    var command = new PixelDrawCommand(
                        PixelDrawCommandType.FillCircle, 0, -1,
                        new int[] { 8, 8, 6 }, Color.HSVToRGB(i / 10f, 1f, 1f));
                    
                    drawEngine.ExecuteDrawCommand(canvas, command);
                    canvas.ApplyChanges();
                    
                    drawEngine.ReturnCanvas(canvas);
                }
                
                float endTime = Time.realtimeSinceStartup;
                float totalTime = endTime - startTime;
                
                AssertTrue(totalTime < 0.1f, $"Performance test (took {totalTime:F3}s)");
                
                PassTest();
            }
            catch (System.Exception e)
            {
                FailTest($"Exception: {e.Message}");
            }
        }
        
        private void TestMemoryManagement()
        {
            StartTest("Memory Management");
            
            try
            {
                // 測試對象池
                PixelCanvas canvas1 = drawEngine.CreateCanvas(32, 32);
                PixelCanvas canvas2 = drawEngine.CreateCanvas(32, 32);
                
                AssertNotNull(canvas1, "First canvas creation");
                AssertNotNull(canvas2, "Second canvas creation");
                AssertTrue(canvas1 != canvas2, "Different canvas instances");
                
                // 歸還到池中
                drawEngine.ReturnCanvas(canvas1);
                drawEngine.ReturnCanvas(canvas2);
                
                // 重新獲取，應該重用
                PixelCanvas canvas3 = drawEngine.CreateCanvas(32, 32);
                AssertNotNull(canvas3, "Canvas reuse from pool");
                
                drawEngine.ReturnCanvas(canvas3);
                
                PassTest();
            }
            catch (System.Exception e)
            {
                FailTest($"Exception: {e.Message}");
            }
        }
        
        private void DrawTestCharacter(PixelCanvas canvas)
        {
            // 繪製一個簡單的角色（史萊姆）
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { 16, 20, 10 }, new Color(0.2f, 0.8f, 0.2f, 1f));
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 眼睛
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { 12, 18 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { 20, 18 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            // 嘴巴
            var mouthCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { 14, 15, 18, 15 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, mouthCommand);
            
            canvas.ApplyChanges();
        }
        
        private void CreateTestSprite(PixelCanvas canvas, string name, Vector3 position)
        {
            GameObject spriteObj = new GameObject(name);
            spriteObj.transform.parent = testResultParent.transform;
            spriteObj.transform.position = position;
            
            SpriteRenderer renderer = spriteObj.AddComponent<SpriteRenderer>();
            
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                pixelPerfectManager.GetScaleFactor() * 16f
            );
            
            renderer.sprite = sprite;
            // SpriteRenderer不直接支援filterMode，由Sprite的紋理控制
            // renderer.filterMode = FilterMode.Point;
        }
        
        private void ClearPreviousResults()
        {
            if (testResultParent != null)
            {
                for (int i = testResultParent.transform.childCount - 1; i >= 0; i--)
                {
                    DestroyImmediate(testResultParent.transform.GetChild(i).gameObject);
                }
            }
        }
        
        // Test framework methods
        private void StartTest(string testName)
        {
            testsRun++;
            Debug.Log($"🧪 Running test: {testName}");
        }
        
        private void PassTest()
        {
            testsPassed++;
            Debug.Log("✅ Test PASSED");
        }
        
        private void FailTest(string reason)
        {
            testsFailed++;
            Debug.LogError($"❌ Test FAILED: {reason}");
        }
        
        private void AssertTrue(bool condition, string message)
        {
            if (!condition)
            {
                throw new System.Exception($"Assertion failed: {message}");
            }
        }
        
        private void AssertNotNull(object obj, string message)
        {
            if (obj == null)
            {
                throw new System.Exception($"Null assertion failed: {message}");
            }
        }
        
        private void AssertColorEqual(Color expected, Color actual, string message)
        {
            float tolerance = 0.01f;
            if (Mathf.Abs(expected.r - actual.r) > tolerance ||
                Mathf.Abs(expected.g - actual.g) > tolerance ||
                Mathf.Abs(expected.b - actual.b) > tolerance ||
                Mathf.Abs(expected.a - actual.a) > tolerance)
            {
                throw new System.Exception($"Color assertion failed: {message}. Expected {expected}, got {actual}");
            }
        }
        
        private void DisplayTestSummary()
        {
            Debug.Log("=== Test Summary ===");
            Debug.Log($"Tests Run: {testsRun}");
            Debug.Log($"Tests Passed: {testsPassed}");
            Debug.Log($"Tests Failed: {testsFailed}");
            Debug.Log($"Success Rate: {(testsPassed / (float)testsRun * 100):F1}%");
            
            if (testsFailed == 0)
            {
                Debug.Log("🎉 All tests passed! Pixel rendering system is working correctly.");
            }
            else
            {
                Debug.LogWarning($"⚠️ {testsFailed} test(s) failed. Please check the implementation.");
            }
        }
        
        // Public methods for manual testing
        [ContextMenu("Run All Tests")]
        public void RunTestsManually()
        {
            RunAllTests();
        }
        
        [ContextMenu("Clear Test Results")]
        public void ClearResults()
        {
            ClearPreviousResults();
        }
        
        private void Update()
        {
            // 快捷鍵測試
            if (UnityEngine.Input.GetKeyDown(KeyCode.T))
            {
                RunAllTests();
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.C))
            {
                ClearResults();
            }
        }
    }
}
