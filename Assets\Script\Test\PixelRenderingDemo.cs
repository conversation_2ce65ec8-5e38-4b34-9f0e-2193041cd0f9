using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 像素渲染系統演示
    /// 展示各種像素繪製功能和解析度適配
    /// </summary>
    public class PixelRenderingDemo : MonoBehaviour
    {
        [Header("Demo Settings")]
        [SerializeField] private bool autoStartDemo = true;
        [SerializeField] private float animationSpeed = 1f;
        [SerializeField] private int characterSize = 32;
        
        [Header("Demo Characters")]
        [SerializeField] private Transform characterParent;
        
        private PixelPerfectManager pixelPerfectManager;
        private PixelDrawEngine drawEngine;
        
        // Demo characters
        private DemoCharacter[] demoCharacters;
        private float animationTime = 0f;
        
        private void Start()
        {
            InitializeDemo();
            
            if (autoStartDemo)
            {
                CreateDemoCharacters();
            }
        }
        
        private void InitializeDemo()
        {
            Debug.Log("=== Initializing Pixel Rendering Demo ===");
            
            // 確保有PixelPerfectManager
            pixelPerfectManager = PixelPerfectManager.Instance;
            if (pixelPerfectManager == null)
            {
                GameObject managerObj = new GameObject("PixelPerfectManager");
                pixelPerfectManager = managerObj.AddComponent<PixelPerfectManager>();
                Debug.Log("✓ Created PixelPerfectManager");
            }
            
            // 獲取PixelDrawEngine
            drawEngine = PixelDrawEngine.Instance;
            Debug.Log("✓ PixelDrawEngine ready");
            
            // 創建角色父物件
            if (characterParent == null)
            {
                GameObject parentObj = new GameObject("Demo Characters");
                characterParent = parentObj.transform;
            }
            
            Debug.Log("✓ Demo initialized");
        }
        
        private void CreateDemoCharacters()
        {
            Debug.Log("Creating demo characters...");
            
            // 創建不同類型的角色
            demoCharacters = new DemoCharacter[]
            {
                CreateSlimeCharacter(new Vector3(-3, 0, 0), Color.green),
                CreateGoblinCharacter(new Vector3(-1, 0, 0), Color.yellow),
                CreateElementalCharacter(new Vector3(1, 0, 0), Color.red),
                CreateKnightCharacter(new Vector3(3, 0, 0), Color.blue)
            };
            
            Debug.Log($"✓ Created {demoCharacters.Length} demo characters");
        }
        
        private DemoCharacter CreateSlimeCharacter(Vector3 position, Color color)
        {
            PixelCanvas canvas = drawEngine.CreateCanvas(characterSize, characterSize);
            
            // 繪製史萊姆身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { characterSize/2, characterSize/2 + 4, 12 }, color);
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 繪製眼睛
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { characterSize/2 - 4, characterSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { characterSize/2 + 4, characterSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            // 繪製嘴巴
            var mouthCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { characterSize/2 - 2, characterSize/2 - 2, characterSize/2 + 2, characterSize/2 - 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, mouthCommand);
            
            canvas.ApplyChanges();
            
            return CreateCharacterGameObject("Slime", canvas, position);
        }
        
        private DemoCharacter CreateGoblinCharacter(Vector3 position, Color color)
        {
            PixelCanvas canvas = drawEngine.CreateCanvas(characterSize, characterSize);
            
            // 繪製身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { characterSize/2 - 4, characterSize/2 + 2, 8, 12 }, color);
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 繪製頭部
            var headCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { characterSize/2, characterSize/2 - 4, 6 }, Color.Lerp(color, Color.white, 0.3f));
            drawEngine.ExecuteDrawCommand(canvas, headCommand);
            
            // 繪製眼睛
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { characterSize/2 - 2, characterSize/2 - 4 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { characterSize/2 + 2, characterSize/2 - 4 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            canvas.ApplyChanges();
            
            return CreateCharacterGameObject("Goblin", canvas, position);
        }
        
        private DemoCharacter CreateElementalCharacter(Vector3 position, Color color)
        {
            PixelCanvas canvas = drawEngine.CreateCanvas(characterSize, characterSize);
            
            // 繪製火焰核心
            var coreCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { characterSize/2, characterSize/2 + 2, 8 }, color);
            drawEngine.ExecuteDrawCommand(canvas, coreCommand);
            
            // 繪製火焰粒子
            var particlesCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawParticles, 1, -1,
                new int[] { characterSize/2, characterSize/2 - 4, 8, 6 }, Color.yellow);
            drawEngine.ExecuteDrawCommand(canvas, particlesCommand);
            
            // 繪製漸變光暈
            var auraCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawGradientCircle, 2, -1,
                new int[] { characterSize/2, characterSize/2 + 2, 14 }, new Color(color.r, color.g, color.b, 0.3f));
            drawEngine.ExecuteDrawCommand(canvas, auraCommand);
            
            canvas.ApplyChanges();
            
            return CreateCharacterGameObject("Elemental", canvas, position);
        }
        
        private DemoCharacter CreateKnightCharacter(Vector3 position, Color color)
        {
            PixelCanvas canvas = drawEngine.CreateCanvas(characterSize, characterSize);
            
            // 繪製身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { characterSize/2 - 5, characterSize/2 + 2, 10, 14 }, color);
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 繪製頭盔
            var helmetCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { characterSize/2 - 4, characterSize/2 - 6, 8, 8 }, Color.gray);
            drawEngine.ExecuteDrawCommand(canvas, helmetCommand);
            
            // 繪製盾牌
            var shieldCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawRect, 1, -1,
                new int[] { characterSize/2 - 8, characterSize/2, 4, 8, 1 }, Color.white);
            drawEngine.ExecuteDrawCommand(canvas, shieldCommand);
            
            // 繪製劍
            var swordCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { characterSize/2 + 6, characterSize/2 - 2, characterSize/2 + 6, characterSize/2 + 10 }, Color.white);
            drawEngine.ExecuteDrawCommand(canvas, swordCommand);
            
            canvas.ApplyChanges();
            
            return CreateCharacterGameObject("Knight", canvas, position);
        }
        
        private DemoCharacter CreateCharacterGameObject(string name, PixelCanvas canvas, Vector3 position)
        {
            GameObject characterObj = new GameObject($"Demo_{name}");
            characterObj.transform.parent = characterParent;
            characterObj.transform.position = position;
            
            SpriteRenderer renderer = characterObj.AddComponent<SpriteRenderer>();
            
            // 創建精靈
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                pixelPerfectManager.GetScaleFactor() * 16f
            );
            
            renderer.sprite = sprite;
            // SpriteRenderer不直接支援filterMode，由Sprite的紋理控制
            // renderer.filterMode = FilterMode.Point;
            
            // 創建DemoCharacter組件
            DemoCharacter demoChar = characterObj.AddComponent<DemoCharacter>();
            demoChar.Initialize(canvas, drawEngine);
            
            return demoChar;
        }
        
        private void Update()
        {
            // 更新動畫
            animationTime += Time.deltaTime * animationSpeed;
            
            if (demoCharacters != null)
            {
                foreach (var character in demoCharacters)
                {
                    if (character != null)
                    {
                        character.UpdateAnimation(animationTime);
                    }
                }
            }
            
            // 快捷鍵控制
            HandleInput();
        }
        
        private void HandleInput()
        {
            if (UnityEngine.Input.GetKeyDown(KeyCode.R))
            {
                RecreateCharacters();
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.P))
            {
                TogglePause();
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.Alpha1))
            {
                SetAnimationSpeed(0.5f);
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.Alpha2))
            {
                SetAnimationSpeed(1f);
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.Alpha3))
            {
                SetAnimationSpeed(2f);
            }
        }
        
        public void RecreateCharacters()
        {
            Debug.Log("Recreating demo characters...");
            
            // 清理現有角色
            if (demoCharacters != null)
            {
                foreach (var character in demoCharacters)
                {
                    if (character != null)
                    {
                        character.Cleanup();
                        DestroyImmediate(character.gameObject);
                    }
                }
            }
            
            // 重新創建
            CreateDemoCharacters();
        }
        
        public void TogglePause()
        {
            animationSpeed = animationSpeed > 0 ? 0 : 1f;
            Debug.Log($"Animation {(animationSpeed > 0 ? "resumed" : "paused")}");
        }
        
        public void SetAnimationSpeed(float speed)
        {
            animationSpeed = speed;
            Debug.Log($"Animation speed set to {speed}x");
        }
        
        private void OnDestroy()
        {
            if (demoCharacters != null)
            {
                foreach (var character in demoCharacters)
                {
                    if (character != null)
                    {
                        character.Cleanup();
                    }
                }
            }
        }
        
        // 在Inspector中顯示控制按鈕
        [ContextMenu("Recreate Characters")]
        public void RecreateCharactersMenu()
        {
            RecreateCharacters();
        }
        
        [ContextMenu("Toggle Pause")]
        public void TogglePauseMenu()
        {
            TogglePause();
        }
    }
    
    /// <summary>
    /// 演示角色組件
    /// </summary>
    public class DemoCharacter : MonoBehaviour
    {
        private PixelCanvas canvas;
        private PixelDrawEngine drawEngine;
        private SpriteRenderer spriteRenderer;
        private Vector3 originalPosition;
        private float bobOffset;
        
        public void Initialize(PixelCanvas canvas, PixelDrawEngine drawEngine)
        {
            this.canvas = canvas;
            this.drawEngine = drawEngine;
            this.spriteRenderer = GetComponent<SpriteRenderer>();
            this.originalPosition = transform.position;
            this.bobOffset = Random.Range(0f, Mathf.PI * 2f);
        }
        
        public void UpdateAnimation(float time)
        {
            // 簡單的上下浮動動畫
            float bobAmount = Mathf.Sin(time + bobOffset) * 0.1f;
            transform.position = originalPosition + Vector3.up * bobAmount;
            
            // 可以在這裡添加更複雜的動畫，如重新繪製角色
        }
        
        public void Cleanup()
        {
            if (canvas != null && drawEngine != null)
            {
                drawEngine.ReturnCanvas(canvas);
            }
        }
    }
}
