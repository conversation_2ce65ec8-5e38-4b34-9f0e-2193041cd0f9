using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 快速像素渲染測試
    /// 用於驗證基本功能是否正常工作
    /// </summary>
    public class QuickPixelTest : MonoBehaviour
    {
        [Header("Quick Test Settings")]
        [SerializeField] private bool runOnStart = true;
        [SerializeField] private int testSize = 32;
        
        private void Start()
        {
            if (runOnStart)
            {
                RunQuickTest();
            }
        }
        
        [ContextMenu("Run Quick Test")]
        public void RunQuickTest()
        {
            Debug.Log("=== Running Quick Pixel Test ===");
            
            try
            {
                // 測試1: 基本像素繪製
                TestBasicPixelDrawing();
                
                // 測試2: 繪製指令系統
                TestDrawCommands();
                
                // 測試3: 像素完美管理器
                TestPixelPerfectManager();
                
                Debug.Log("✅ Quick test completed successfully!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Quick test failed: {e.Message}");
                Debug.LogException(e);
            }
        }
        
        private void TestBasicPixelDrawing()
        {
            Debug.Log("Testing basic pixel drawing...");
            
            // 創建畫布
            PixelDrawEngine engine = PixelDrawEngine.Instance;
            PixelCanvas canvas = engine.CreateCanvas(testSize, testSize);
            
            // 設置一些像素
            canvas.SetPixel(5, 5, Color.red);
            canvas.SetPixel(10, 10, Color.green);
            canvas.SetPixel(15, 15, Color.blue);
            
            // 驗證像素
            Color red = canvas.GetPixel(5, 5);
            Color green = canvas.GetPixel(10, 10);
            Color blue = canvas.GetPixel(15, 15);
            
            if (red != Color.red || green != Color.green || blue != Color.blue)
            {
                throw new System.Exception("Pixel colors don't match expected values");
            }
            
            // 應用變更並創建精靈
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "BasicPixelTest", Vector3.zero);
            
            // 歸還畫布
            engine.ReturnCanvas(canvas);
            
            Debug.Log("✓ Basic pixel drawing test passed");
        }
        
        private void TestDrawCommands()
        {
            Debug.Log("Testing draw commands...");
            
            PixelDrawEngine engine = PixelDrawEngine.Instance;
            PixelCanvas canvas = engine.CreateCanvas(testSize, testSize);
            
            // 測試填充圓形
            var circleCmd = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { testSize/2, testSize/2, 8 }, Color.yellow);
            engine.ExecuteDrawCommand(canvas, circleCmd);
            
            // 測試繪製線條
            var lineCmd = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { 0, 0, testSize-1, testSize-1 }, Color.red);
            engine.ExecuteDrawCommand(canvas, lineCmd);
            
            // 測試填充矩形
            var rectCmd = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { 2, 2, 6, 4 }, Color.blue);
            engine.ExecuteDrawCommand(canvas, rectCmd);
            
            // 測試粒子效果
            var particleCmd = new PixelDrawCommand(
                PixelDrawCommandType.DrawParticles, 2, -1,
                new int[] { testSize/2, testSize/4, 5, 3 }, Color.white);
            engine.ExecuteDrawCommand(canvas, particleCmd);
            
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "DrawCommandsTest", new Vector3(2, 0, 0));
            
            engine.ReturnCanvas(canvas);
            
            Debug.Log("✓ Draw commands test passed");
        }
        
        private void TestPixelPerfectManager()
        {
            Debug.Log("Testing PixelPerfectManager...");
            
            // 獲取或創建PixelPerfectManager
            PixelPerfectManager manager = PixelPerfectManager.Instance;
            if (manager == null)
            {
                GameObject managerObj = new GameObject("PixelPerfectManager");
                manager = managerObj.AddComponent<PixelPerfectManager>();
            }
            
            // 測試基本屬性
            float pixelSize = manager.GetPixelSize();
            int scaleFactor = manager.GetScaleFactor();
            Vector2 baseResolution = manager.GetBaseResolution();
            
            if (pixelSize <= 0)
            {
                throw new System.Exception("Invalid pixel size");
            }
            
            if (scaleFactor < 1)
            {
                throw new System.Exception("Invalid scale factor");
            }
            
            if (baseResolution.x <= 0 || baseResolution.y <= 0)
            {
                throw new System.Exception("Invalid base resolution");
            }
            
            Debug.Log($"PixelPerfectManager - Pixel Size: {pixelSize}, Scale Factor: {scaleFactor}, Base Resolution: {baseResolution}");
            Debug.Log("✓ PixelPerfectManager test passed");
        }
        
        private void CreateTestSprite(PixelCanvas canvas, string name, Vector3 position)
        {
            GameObject spriteObj = new GameObject(name);
            spriteObj.transform.position = position;
            
            SpriteRenderer renderer = spriteObj.AddComponent<SpriteRenderer>();
            
            // 創建精靈
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                16f // pixels per unit
            );
            
            renderer.sprite = sprite;
            // SpriteRenderer不直接支援filterMode，由Sprite的紋理控制
            // renderer.filterMode = FilterMode.Point;
            
            Debug.Log($"Created test sprite: {name} at {position}");
        }
        
        private void Update()
        {
            // 快捷鍵
            if (UnityEngine.Input.GetKeyDown(KeyCode.Q))
            {
                RunQuickTest();
            }

            if (UnityEngine.Input.GetKeyDown(KeyCode.X))
            {
                ClearTestSprites();
            }
        }
        
        [ContextMenu("Clear Test Sprites")]
        public void ClearTestSprites()
        {
            // 清理測試精靈
            GameObject[] testObjects = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in testObjects)
            {
                if (obj.name.Contains("Test"))
                {
                    DestroyImmediate(obj);
                }
            }
            
            Debug.Log("Test sprites cleared");
        }
        
        private void OnGUI()
        {
            // 簡單的GUI控制
            GUILayout.BeginArea(new Rect(10, 10, 200, 100));
            
            if (GUILayout.Button("Run Quick Test (Q)"))
            {
                RunQuickTest();
            }
            
            if (GUILayout.Button("Clear Sprites (X)"))
            {
                ClearTestSprites();
            }
            
            GUILayout.Label($"Test Size: {testSize}");
            
            GUILayout.EndArea();
        }
    }
}
