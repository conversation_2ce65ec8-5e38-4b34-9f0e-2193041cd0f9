using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using PetingGame.Rendering;

namespace PetingGame.Input
{
    /// <summary>
    /// 移動設備輸入管理器，處理觸控輸入並轉換為遊戲操作
    /// </summary>
    public class MobileInputManager : MonoBehaviour
    {
        [Header("Touch Settings")]
        [SerializeField] private float tapThreshold = 0.3f; // 點擊時間閾值
        [SerializeField] private float swipeThreshold = 50f; // 滑動距離閾值（像素）
        [SerializeField] private float longPressThreshold = 1f; // 長按時間閾值
        
        [Header("Console Navigation")]
        [SerializeField] private float navigationCooldown = 0.2f; // 導航冷卻時間
        [SerializeField] private bool enableHapticFeedback = true;
        
        // Touch tracking
        private Dictionary<int, TouchInfo> activeTouches = new Dictionary<int, TouchInfo>();
        private float lastNavigationTime;
        
        // Events
        public System.Action<Vector2> OnTap;
        public System.Action<Vector2, Vector2> OnSwipe;
        public System.Action<Vector2> OnLongPress;
        public System.Action<ConsoleDirection> OnConsoleNavigation;
        public System.Action OnConsoleSelect;
        public System.Action OnConsoleBack;
        
        private void Update()
        {
            ProcessTouchInput();
            ProcessKeyboardInput(); // 用於編輯器測試
        }
        
        private void ProcessTouchInput()
        {
            // 處理所有觸控點
            for (int i = 0; i < UnityEngine.Input.touchCount; i++)
            {
                Touch touch = UnityEngine.Input.GetTouch(i);
                ProcessTouch(touch);
            }
            
            // 處理滑鼠輸入（用於編輯器測試）
            if (Application.isEditor)
            {
                ProcessMouseInput();
            }
        }
        
        private void ProcessTouch(Touch touch)
        {
            Vector2 screenPosition = touch.position;
            Vector2 worldPosition = ConvertScreenToWorldPosition(screenPosition);
            
            switch (touch.phase)
            {
                case TouchPhase.Began:
                    HandleTouchBegan(touch.fingerId, screenPosition, worldPosition);
                    break;
                    
                case TouchPhase.Moved:
                    HandleTouchMoved(touch.fingerId, screenPosition, worldPosition);
                    break;
                    
                case TouchPhase.Ended:
                    HandleTouchEnded(touch.fingerId, screenPosition, worldPosition);
                    break;
                    
                case TouchPhase.Canceled:
                    HandleTouchCanceled(touch.fingerId);
                    break;
            }
        }
        
        private void ProcessMouseInput()
        {
            Vector2 mousePosition = UnityEngine.Input.mousePosition;
            Vector2 worldPosition = ConvertScreenToWorldPosition(mousePosition);
            
            if (UnityEngine.Input.GetMouseButtonDown(0))
            {
                HandleTouchBegan(0, mousePosition, worldPosition);
            }
            else if (UnityEngine.Input.GetMouseButton(0))
            {
                HandleTouchMoved(0, mousePosition, worldPosition);
            }
            else if (UnityEngine.Input.GetMouseButtonUp(0))
            {
                HandleTouchEnded(0, mousePosition, worldPosition);
            }
        }
        
        private void HandleTouchBegan(int fingerId, Vector2 screenPosition, Vector2 worldPosition)
        {
            TouchInfo touchInfo = new TouchInfo
            {
                fingerId = fingerId,
                startScreenPosition = screenPosition,
                startWorldPosition = worldPosition,
                currentScreenPosition = screenPosition,
                currentWorldPosition = worldPosition,
                startTime = Time.time,
                isActive = true
            };
            
            activeTouches[fingerId] = touchInfo;
            
            // 提供觸覺反饋
            if (enableHapticFeedback)
            {
                Handheld.Vibrate();
            }
        }
        
        private void HandleTouchMoved(int fingerId, Vector2 screenPosition, Vector2 worldPosition)
        {
            if (!activeTouches.ContainsKey(fingerId)) return;
            
            TouchInfo touchInfo = activeTouches[fingerId];
            touchInfo.currentScreenPosition = screenPosition;
            touchInfo.currentWorldPosition = worldPosition;
            
            // 檢查是否為滑動手勢
            Vector2 deltaScreen = screenPosition - touchInfo.startScreenPosition;
            float distance = deltaScreen.magnitude;
            
            if (distance > swipeThreshold && !touchInfo.hasTriggeredSwipe)
            {
                ConsoleDirection direction = GetSwipeDirection(deltaScreen);
                HandleConsoleNavigation(direction);
                touchInfo.hasTriggeredSwipe = true;
            }
        }
        
        private void HandleTouchEnded(int fingerId, Vector2 screenPosition, Vector2 worldPosition)
        {
            if (!activeTouches.ContainsKey(fingerId)) return;
            
            TouchInfo touchInfo = activeTouches[fingerId];
            float touchDuration = Time.time - touchInfo.startTime;
            Vector2 deltaScreen = screenPosition - touchInfo.startScreenPosition;
            float distance = deltaScreen.magnitude;
            
            // 判斷手勢類型
            if (touchDuration >= longPressThreshold)
            {
                // 長按
                OnLongPress?.Invoke(worldPosition);
            }
            else if (distance < swipeThreshold && touchDuration < tapThreshold)
            {
                // 點擊
                if (IsUIElement(screenPosition))
                {
                    OnConsoleSelect?.Invoke();
                }
                else
                {
                    OnTap?.Invoke(worldPosition);
                }
            }
            else if (distance >= swipeThreshold && !touchInfo.hasTriggeredSwipe)
            {
                // 滑動（如果在移動中沒有觸發）
                OnSwipe?.Invoke(touchInfo.startWorldPosition, worldPosition);
                
                ConsoleDirection direction = GetSwipeDirection(deltaScreen);
                HandleConsoleNavigation(direction);
            }
            
            activeTouches.Remove(fingerId);
        }
        
        private void HandleTouchCanceled(int fingerId)
        {
            activeTouches.Remove(fingerId);
        }
        
        private ConsoleDirection GetSwipeDirection(Vector2 delta)
        {
            float absX = Mathf.Abs(delta.x);
            float absY = Mathf.Abs(delta.y);
            
            if (absX > absY)
            {
                return delta.x > 0 ? ConsoleDirection.Right : ConsoleDirection.Left;
            }
            else
            {
                return delta.y > 0 ? ConsoleDirection.Up : ConsoleDirection.Down;
            }
        }
        
        private void HandleConsoleNavigation(ConsoleDirection direction)
        {
            if (Time.time - lastNavigationTime < navigationCooldown) return;
            
            lastNavigationTime = Time.time;
            OnConsoleNavigation?.Invoke(direction);
            
            // 觸覺反饋
            if (enableHapticFeedback)
            {
                Handheld.Vibrate();
            }
        }
        
        private Vector2 ConvertScreenToWorldPosition(Vector2 screenPosition)
        {
            if (PixelPerfectManager.Instance != null)
            {
                return PixelPerfectManager.Instance.ScreenToWorldPoint(screenPosition);
            }
            
            Camera cam = Camera.main ?? FindObjectOfType<Camera>();
            if (cam != null)
            {
                return cam.ScreenToWorldPoint(new Vector3(screenPosition.x, screenPosition.y, cam.nearClipPlane));
            }
            
            return screenPosition;
        }
        
        private bool IsUIElement(Vector2 screenPosition)
        {
            // 檢查是否點擊在UI元素上
            PointerEventData eventData = new PointerEventData(EventSystem.current)
            {
                position = screenPosition
            };
            
            List<RaycastResult> results = new List<RaycastResult>();
            EventSystem.current.RaycastAll(eventData, results);
            
            return results.Count > 0;
        }
        
        private void ProcessKeyboardInput()
        {
            // 鍵盤輸入用於編輯器測試
            if (UnityEngine.Input.GetKeyDown(KeyCode.UpArrow))
                OnConsoleNavigation?.Invoke(ConsoleDirection.Up);
            if (UnityEngine.Input.GetKeyDown(KeyCode.DownArrow))
                OnConsoleNavigation?.Invoke(ConsoleDirection.Down);
            if (UnityEngine.Input.GetKeyDown(KeyCode.LeftArrow))
                OnConsoleNavigation?.Invoke(ConsoleDirection.Left);
            if (UnityEngine.Input.GetKeyDown(KeyCode.RightArrow))
                OnConsoleNavigation?.Invoke(ConsoleDirection.Right);
            if (UnityEngine.Input.GetKeyDown(KeyCode.Return) || UnityEngine.Input.GetKeyDown(KeyCode.Space))
                OnConsoleSelect?.Invoke();
            if (UnityEngine.Input.GetKeyDown(KeyCode.Escape))
                OnConsoleBack?.Invoke();
        }
        
        public void SetHapticFeedback(bool enabled)
        {
            enableHapticFeedback = enabled;
        }
        
        public void SetNavigationCooldown(float cooldown)
        {
            navigationCooldown = cooldown;
        }
        
        public bool IsAnyTouchActive()
        {
            return activeTouches.Count > 0;
        }
        
        public Vector2 GetPrimaryTouchPosition()
        {
            if (activeTouches.Count > 0)
            {
                foreach (var touch in activeTouches.Values)
                {
                    return touch.currentWorldPosition;
                }
            }
            return Vector2.zero;
        }
    }
    
    /// <summary>
    /// 觸控信息結構
    /// </summary>
    public class TouchInfo
    {
        public int fingerId;
        public Vector2 startScreenPosition;
        public Vector2 startWorldPosition;
        public Vector2 currentScreenPosition;
        public Vector2 currentWorldPosition;
        public float startTime;
        public bool isActive;
        public bool hasTriggeredSwipe;
    }
    
    /// <summary>
    /// Console導航方向
    /// </summary>
    public enum ConsoleDirection
    {
        Up,
        Down,
        Left,
        Right
    }
}
