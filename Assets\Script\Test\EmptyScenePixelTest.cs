using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 空場景像素渲染測試
    /// 專門用於在空場景中測試像素渲染功能的完整流程
    /// </summary>
    public class EmptyScenePixelTest : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool autoStartTest = true;
        [SerializeField] private float testDelay = 1f;
        [SerializeField] private int canvasSize = 64;
        
        [Header("Test Results")]
        [SerializeField] private bool testCompleted = false;
        [SerializeField] private bool testPassed = false;
        [SerializeField] private string testStatus = "Not Started";
        
        private PixelPerfectManager pixelPerfectManager;
        private PixelDrawEngine drawEngine;
        
        private void Start()
        {
            Debug.Log("=== Empty Scene Pixel Test Started ===");
            testStatus = "Initializing...";
            
            if (autoStartTest)
            {
                Invoke(nameof(RunCompleteTest), testDelay);
            }
        }
        
        [ContextMenu("Run Complete Test")]
        public void RunCompleteTest()
        {
            StartCoroutine(RunTestSequence());
        }
        
        private System.Collections.IEnumerator RunTestSequence()
        {
            testCompleted = false;
            testPassed = false;
            testStatus = "Running Tests...";
            
            try
            {
                // 步驟1: 初始化系統
                yield return StartCoroutine(InitializePixelRenderingSystem());
                
                // 步驟2: 測試基本像素繪製
                yield return StartCoroutine(TestBasicPixelDrawing());
                
                // 步驟3: 測試繪製指令
                yield return StartCoroutine(TestDrawCommands());
                
                // 步驟4: 測試解析度適配
                yield return StartCoroutine(TestResolutionAdaptation());
                
                // 步驟5: 測試角色創建
                yield return StartCoroutine(TestCharacterCreation());
                
                // 步驟6: 測試性能
                yield return StartCoroutine(TestPerformance());
                
                testPassed = true;
                testStatus = "All Tests Passed!";
                Debug.Log("🎉 All pixel rendering tests completed successfully!");
                
            }
            catch (System.Exception e)
            {
                testPassed = false;
                testStatus = $"Test Failed: {e.Message}";
                Debug.LogError($"❌ Pixel rendering test failed: {e.Message}");
                Debug.LogException(e);
            }
            finally
            {
                testCompleted = true;
            }
        }
        
        private System.Collections.IEnumerator InitializePixelRenderingSystem()
        {
            Debug.Log("🔧 Step 1: Initializing Pixel Rendering System...");
            testStatus = "Initializing System...";
            
            // 確保有PixelPerfectManager
            pixelPerfectManager = PixelPerfectManager.Instance;
            if (pixelPerfectManager == null)
            {
                GameObject managerObj = GameObject.Find("PixelPerfectManager");
                if (managerObj == null)
                {
                    managerObj = new GameObject("PixelPerfectManager");
                }
                pixelPerfectManager = managerObj.GetComponent<PixelPerfectManager>();
                if (pixelPerfectManager == null)
                {
                    pixelPerfectManager = managerObj.AddComponent<PixelPerfectManager>();
                }
                Debug.Log("✓ PixelPerfectManager created and initialized");
            }
            else
            {
                Debug.Log("✓ PixelPerfectManager found");
            }
            
            yield return new WaitForSeconds(0.5f);
            
            // 獲取PixelDrawEngine
            drawEngine = PixelDrawEngine.Instance;
            if (drawEngine == null)
            {
                throw new System.Exception("Failed to get PixelDrawEngine instance");
            }
            Debug.Log("✓ PixelDrawEngine instance obtained");
            
            // 驗證系統設置
            float pixelSize = pixelPerfectManager.GetPixelSize();
            int scaleFactor = pixelPerfectManager.GetScaleFactor();
            Vector2 baseResolution = pixelPerfectManager.GetBaseResolution();
            
            Debug.Log($"✓ System initialized - Pixel Size: {pixelSize}, Scale Factor: {scaleFactor}, Base Resolution: {baseResolution}");
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private System.Collections.IEnumerator TestBasicPixelDrawing()
        {
            Debug.Log("🎨 Step 2: Testing Basic Pixel Drawing...");
            testStatus = "Testing Basic Drawing...";
            
            // 創建測試畫布
            PixelCanvas canvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            if (canvas == null)
            {
                throw new System.Exception("Failed to create canvas");
            }
            Debug.Log($"✓ Canvas created ({canvasSize}x{canvasSize})");
            
            // 測試像素設置
            canvas.SetPixel(10, 10, Color.red);
            canvas.SetPixel(20, 20, Color.green);
            canvas.SetPixel(30, 30, Color.blue);
            canvas.SetPixel(40, 40, Color.yellow);
            
            // 驗證像素
            Color testColor = canvas.GetPixel(10, 10);
            if (testColor != Color.red)
            {
                throw new System.Exception($"Pixel verification failed. Expected: {Color.red}, Got: {testColor}");
            }
            Debug.Log("✓ Pixel set/get verification passed");
            
            // 應用變更並創建精靈
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "BasicPixelTest", new Vector3(-3, 2, 0));
            
            drawEngine.ReturnCanvas(canvas);
            Debug.Log("✓ Basic pixel drawing test completed");
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private System.Collections.IEnumerator TestDrawCommands()
        {
            Debug.Log("🖌️ Step 3: Testing Draw Commands...");
            testStatus = "Testing Draw Commands...";
            
            PixelCanvas canvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            
            // 測試各種繪製指令
            var commands = new PixelDrawCommand[]
            {
                // 填充圓形
                new PixelDrawCommand(PixelDrawCommandType.FillCircle, 0, -1,
                    new int[] { canvasSize/2, canvasSize/2, 15 }, Color.cyan),
                
                // 繪製線條
                new PixelDrawCommand(PixelDrawCommandType.DrawLine, 1, -1,
                    new int[] { 5, 5, canvasSize-5, canvasSize-5 }, Color.red),
                
                // 填充矩形
                new PixelDrawCommand(PixelDrawCommandType.FillRect, 0, -1,
                    new int[] { 5, canvasSize-15, 10, 8 }, Color.blue),
                
                // 粒子效果
                new PixelDrawCommand(PixelDrawCommandType.DrawParticles, 2, -1,
                    new int[] { canvasSize/2, canvasSize/4, 8, 5 }, Color.white)
            };
            
            foreach (var command in commands)
            {
                drawEngine.ExecuteDrawCommand(canvas, command);
                Debug.Log($"✓ {command.CommandType} command executed");
            }
            
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "DrawCommandsTest", new Vector3(0, 2, 0));
            
            drawEngine.ReturnCanvas(canvas);
            Debug.Log("✓ Draw commands test completed");
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private System.Collections.IEnumerator TestResolutionAdaptation()
        {
            Debug.Log("📱 Step 4: Testing Resolution Adaptation...");
            testStatus = "Testing Resolution...";
            
            // 測試座標轉換
            Vector3 worldPos = new Vector3(1, 1, 0);
            Vector3 screenPos = pixelPerfectManager.WorldToScreenPoint(worldPos);
            Vector3 backToWorld = pixelPerfectManager.ScreenToWorldPoint(screenPos);
            
            float tolerance = 0.1f;
            if (Vector3.Distance(worldPos, backToWorld) > tolerance)
            {
                throw new System.Exception($"Coordinate conversion failed. Original: {worldPos}, Converted back: {backToWorld}");
            }
            Debug.Log("✓ Coordinate conversion test passed");
            
            // 測試解析度設置
            Vector2 effectiveResolution = pixelPerfectManager.EffectiveResolution;
            Rect safeArea = pixelPerfectManager.GetSafeArea();
            
            Debug.Log($"✓ Resolution adaptation - Effective: {effectiveResolution}, Safe Area: {safeArea}");
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private System.Collections.IEnumerator TestCharacterCreation()
        {
            Debug.Log("👾 Step 5: Testing Character Creation...");
            testStatus = "Creating Characters...";
            
            // 創建史萊姆角色
            PixelCanvas slimeCanvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            CreateSlimeCharacter(slimeCanvas);
            slimeCanvas.ApplyChanges();
            CreateTestSprite(slimeCanvas, "SlimeCharacter", new Vector3(3, 2, 0));
            drawEngine.ReturnCanvas(slimeCanvas);
            
            yield return new WaitForSeconds(0.3f);
            
            // 創建騎士角色
            PixelCanvas knightCanvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            CreateKnightCharacter(knightCanvas);
            knightCanvas.ApplyChanges();
            CreateTestSprite(knightCanvas, "KnightCharacter", new Vector3(-3, -2, 0));
            drawEngine.ReturnCanvas(knightCanvas);
            
            Debug.Log("✓ Character creation test completed");
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private System.Collections.IEnumerator TestPerformance()
        {
            Debug.Log("⚡ Step 6: Testing Performance...");
            testStatus = "Testing Performance...";
            
            float startTime = Time.realtimeSinceStartup;
            
            // 創建多個小畫布測試性能
            for (int i = 0; i < 10; i++)
            {
                PixelCanvas canvas = drawEngine.CreateCanvas(16, 16);
                
                var command = new PixelDrawCommand(
                    PixelDrawCommandType.FillCircle, 0, -1,
                    new int[] { 8, 8, 6 }, Color.HSVToRGB(i / 10f, 1f, 1f));
                
                drawEngine.ExecuteDrawCommand(canvas, command);
                canvas.ApplyChanges();
                
                drawEngine.ReturnCanvas(canvas);
            }
            
            float endTime = Time.realtimeSinceStartup;
            float totalTime = endTime - startTime;
            
            Debug.Log($"✓ Performance test completed - 10 canvases processed in {totalTime:F3} seconds");
            
            if (totalTime > 0.1f)
            {
                Debug.LogWarning($"Performance warning: Processing took {totalTime:F3}s (expected < 0.1s)");
            }
            
            yield return new WaitForSeconds(0.5f);
        }
        
        private void CreateSlimeCharacter(PixelCanvas canvas)
        {
            // 史萊姆身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { canvasSize/2, canvasSize/2 + 4, 18 }, 
                new Color(0.2f, 0.8f, 0.2f, 1f));
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 眼睛
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { canvasSize/2 - 6, canvasSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { canvasSize/2 + 6, canvasSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            // 嘴巴
            var mouthCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { canvasSize/2 - 4, canvasSize/2 - 4, canvasSize/2 + 4, canvasSize/2 - 4 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, mouthCommand);
        }
        
        private void CreateKnightCharacter(PixelCanvas canvas)
        {
            // 身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { canvasSize/2 - 8, canvasSize/2 + 4, 16, 20 }, Color.blue);
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 頭盔
            var helmetCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { canvasSize/2 - 6, canvasSize/2 - 8, 12, 12 }, Color.gray);
            drawEngine.ExecuteDrawCommand(canvas, helmetCommand);
            
            // 劍
            var swordCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { canvasSize/2 + 12, canvasSize/2 - 4, canvasSize/2 + 12, canvasSize/2 + 16 }, Color.white);
            drawEngine.ExecuteDrawCommand(canvas, swordCommand);
        }
        
        private void CreateTestSprite(PixelCanvas canvas, string name, Vector3 position)
        {
            GameObject spriteObj = new GameObject($"Test_{name}");
            spriteObj.transform.position = position;
            
            SpriteRenderer renderer = spriteObj.AddComponent<SpriteRenderer>();
            
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                pixelPerfectManager.GetScaleFactor() * 16f
            );
            
            renderer.sprite = sprite;
            Debug.Log($"✓ Test sprite '{name}' created at {position}");
        }
        
        [ContextMenu("Clear Test Results")]
        public void ClearTestResults()
        {
            GameObject[] testObjects = FindObjectsOfType<GameObject>();
            int cleared = 0;
            
            foreach (GameObject obj in testObjects)
            {
                if (obj.name.StartsWith("Test_"))
                {
                    DestroyImmediate(obj);
                    cleared++;
                }
            }
            
            testCompleted = false;
            testPassed = false;
            testStatus = "Cleared";
            
            Debug.Log($"Cleared {cleared} test objects");
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            
            GUILayout.Label("Empty Scene Pixel Test", GUI.skin.box);
            GUILayout.Label($"Status: {testStatus}");
            GUILayout.Label($"Completed: {testCompleted}");
            GUILayout.Label($"Passed: {testPassed}");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Run Complete Test"))
            {
                RunCompleteTest();
            }
            
            if (GUILayout.Button("Clear Test Results"))
            {
                ClearTestResults();
            }
            
            GUILayout.EndArea();
        }
    }
}
