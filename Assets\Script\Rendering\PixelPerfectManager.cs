using UnityEngine;
using UnityEngine.Rendering;

namespace PetingGame.Rendering
{
    /// <summary>
    /// 管理像素完美渲染，確保在不同解析度設備上的一致性
    /// </summary>
    public class PixelPerfectManager : MonoBehaviour
    {
        [Header("Base Resolution Settings")]
        [SerializeField] private int baseWidth = 320;
        [SerializeField] private int baseHeight = 240;
        [SerializeField] private int pixelsPerUnit = 16;
        
        [Head<PERSON>("Scaling Settings")]
        [SerializeField] private bool useIntegerScaling = true;
        [SerializeField] private float minScaleFactor = 1f;
        [SerializeField] private float maxScaleFactor = 6f;
        
        [Header("Viewport Settings")]
        [SerializeField] private bool maintainAspectRatio = true;
        [SerializeField] private Color letterboxColor = Color.black;
        
        // Runtime properties
        public int CurrentScaleFactor { get; private set; }
        public Vector2 EffectiveResolution { get; private set; }
        public Rect ViewportRect { get; private set; }
        public float PixelSize { get; private set; }
        
        // Events
        public System.Action<int> OnScaleFactorChanged;
        public System.Action<Vector2> OnResolutionChanged;
        
        private Camera mainCamera;
        private RenderTexture pixelPerfectRT;
        private Material blitMaterial;
        
        private static PixelPerfectManager _instance;
        public static PixelPerfectManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<PixelPerfectManager>();
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePixelPerfectRendering();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupCamera();
            CalculateOptimalSettings();
            CreateRenderTexture();
            SetupBlitMaterial();
        }
        
        private void InitializePixelPerfectRendering()
        {
            // 確保像素完美渲染設置
            QualitySettings.antiAliasing = 0;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;

            // 在Unity 6000中，globalTexelBias已被移除，不需要設置
            // Texture2D.globalTexelBias = 0f; // 已移除
        }
        
        private void SetupCamera()
        {
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
            
            if (mainCamera != null)
            {
                mainCamera.orthographic = true;
                mainCamera.nearClipPlane = -1000f;
                mainCamera.farClipPlane = 1000f;
                mainCamera.backgroundColor = letterboxColor;
            }
        }
        
        private void CalculateOptimalSettings()
        {
            int screenWidth = Screen.width;
            int screenHeight = Screen.height;
            
            // 計算基於寬度和高度的縮放因子
            float scaleX = (float)screenWidth / baseWidth;
            float scaleY = (float)screenHeight / baseHeight;
            
            // 選擇較小的縮放因子以確保完整顯示
            float targetScale = Mathf.Min(scaleX, scaleY);
            
            if (useIntegerScaling)
            {
                // 使用整數縮放確保像素完美
                CurrentScaleFactor = Mathf.Clamp(Mathf.FloorToInt(targetScale), 
                    Mathf.RoundToInt(minScaleFactor), Mathf.RoundToInt(maxScaleFactor));
            }
            else
            {
                CurrentScaleFactor = Mathf.RoundToInt(Mathf.Clamp(targetScale, minScaleFactor, maxScaleFactor));
            }
            
            // 計算有效解析度
            EffectiveResolution = new Vector2(
                baseWidth * CurrentScaleFactor,
                baseHeight * CurrentScaleFactor
            );
            
            // 計算視口矩形（用於letterboxing）
            CalculateViewportRect(screenWidth, screenHeight);
            
            // 計算像素大小
            PixelSize = 1f / pixelsPerUnit;
            
            // 設置相機正交大小
            if (mainCamera != null)
            {
                mainCamera.orthographicSize = (baseHeight / 2f) / pixelsPerUnit;
            }
            
            Debug.Log($"Pixel Perfect Settings - Scale: {CurrentScaleFactor}, " +
                     $"Effective Resolution: {EffectiveResolution}, " +
                     $"Viewport: {ViewportRect}");
            
            // 觸發事件
            OnScaleFactorChanged?.Invoke(CurrentScaleFactor);
            OnResolutionChanged?.Invoke(EffectiveResolution);
        }
        
        private void CalculateViewportRect(int screenWidth, int screenHeight)
        {
            if (!maintainAspectRatio)
            {
                ViewportRect = new Rect(0, 0, 1, 1);
                return;
            }
            
            float targetAspect = (float)baseWidth / baseHeight;
            float screenAspect = (float)screenWidth / screenHeight;
            
            if (screenAspect > targetAspect)
            {
                // 螢幕比目標更寬，需要左右letterbox
                float width = targetAspect / screenAspect;
                float x = (1f - width) / 2f;
                ViewportRect = new Rect(x, 0, width, 1);
            }
            else
            {
                // 螢幕比目標更高，需要上下letterbox
                float height = screenAspect / targetAspect;
                float y = (1f - height) / 2f;
                ViewportRect = new Rect(0, y, 1, height);
            }
        }
        
        private void CreateRenderTexture()
        {
            if (pixelPerfectRT != null)
            {
                pixelPerfectRT.Release();
            }
            
            pixelPerfectRT = new RenderTexture(baseWidth, baseHeight, 24, RenderTextureFormat.ARGB32)
            {
                filterMode = FilterMode.Point,
                wrapMode = TextureWrapMode.Clamp,
                antiAliasing = 1,
                useMipMap = false
            };
            
            pixelPerfectRT.Create();
            
            if (mainCamera != null)
            {
                mainCamera.targetTexture = pixelPerfectRT;
                mainCamera.rect = new Rect(0, 0, 1, 1);
            }
        }
        
        private void SetupBlitMaterial()
        {
            // 創建用於最終渲染的材質
            Shader blitShader = Shader.Find("Hidden/PixelPerfectBlit");
            if (blitShader == null)
            {
                blitShader = Shader.Find("Unlit/Texture");
            }
            
            blitMaterial = new Material(blitShader);
            blitMaterial.mainTexture = pixelPerfectRT;
        }
        
        private void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            if (pixelPerfectRT == null || blitMaterial == null)
            {
                Graphics.Blit(source, destination);
                return;
            }
            
            // 清除目標紋理為letterbox顏色
            RenderTexture.active = destination;
            GL.Clear(true, true, letterboxColor);
            
            // 計算渲染矩形
            Rect renderRect = new Rect(
                ViewportRect.x * Screen.width,
                ViewportRect.y * Screen.height,
                ViewportRect.width * Screen.width,
                ViewportRect.height * Screen.height
            );
            
            // 使用點採樣渲染到最終目標
            Graphics.DrawTexture(renderRect, pixelPerfectRT, blitMaterial);
            
            RenderTexture.active = null;
        }
        
        public Vector3 ScreenToWorldPoint(Vector3 screenPosition)
        {
            // 調整螢幕座標以考慮viewport
            Vector3 adjustedPosition = new Vector3(
                (screenPosition.x - ViewportRect.x * Screen.width) / (ViewportRect.width * Screen.width),
                (screenPosition.y - ViewportRect.y * Screen.height) / (ViewportRect.height * Screen.height),
                screenPosition.z
            );
            
            adjustedPosition.x *= baseWidth;
            adjustedPosition.y *= baseHeight;
            
            return mainCamera.ScreenToWorldPoint(adjustedPosition);
        }
        
        public Vector3 WorldToScreenPoint(Vector3 worldPosition)
        {
            Vector3 screenPos = mainCamera.WorldToScreenPoint(worldPosition);
            
            // 調整到實際螢幕座標
            screenPos.x = screenPos.x / baseWidth * ViewportRect.width * Screen.width + ViewportRect.x * Screen.width;
            screenPos.y = screenPos.y / baseHeight * ViewportRect.height * Screen.height + ViewportRect.y * Screen.height;
            
            return screenPos;
        }
        
        public void RecalculateSettings()
        {
            CalculateOptimalSettings();
            CreateRenderTexture();
            SetupBlitMaterial();
        }
        
        private void OnValidate()
        {
            if (Application.isPlaying)
            {
                RecalculateSettings();
            }
        }
        
        private void OnDestroy()
        {
            if (pixelPerfectRT != null)
            {
                pixelPerfectRT.Release();
            }
            
            if (blitMaterial != null)
            {
                DestroyImmediate(blitMaterial);
            }
        }
        
        // 公共API用於其他系統
        public float GetPixelSize() => PixelSize;
        public int GetScaleFactor() => CurrentScaleFactor;
        public Vector2 GetBaseResolution() => new Vector2(baseWidth, baseHeight);
        public Rect GetSafeArea() => ViewportRect;
    }
}
