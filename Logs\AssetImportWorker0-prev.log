Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Enterprise' Language: 'en' Physical Memory: 32557 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-31T01:30:37Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Workspace/Unity/PetingGame
-logFile
Logs/AssetImportWorker0.log
-srvPort
64650
-job-worker-count
13
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Workspace/Unity/PetingGame
C:/Workspace/Unity/PetingGame
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [3412]  Target information:

Player connection [3412]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 968808737 [EditorId] 968808737 [Version] 1048832 [Id] WindowsEditor(7,Raymond) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [3412]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 968808737 [EditorId] 968808737 [Version] 1048832 [Id] WindowsEditor(7,Raymond) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [3412] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 13
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Workspace/Unity/PetingGame/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 (ID=0x2f04)
    Vendor:   NVIDIA
    VRAM:     11855 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56028
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002906 seconds.
- Loaded All Assemblies, in  0.241 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 245 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.462 seconds
Domain Reload Profiling: 701ms
	BeginReloadAssembly (78ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (101ms)
		LoadAssemblies (77ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (99ms)
			TypeCache.Refresh (97ms)
				TypeCache.ScanAssembly (89ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (462ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (442ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (302ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (72ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.508 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 4 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Using default port 6400
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:69)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 69)

Saved port 6400 to storage
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:SavePort (int) (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:130)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:44)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 130)

UnityMcpBridge started on port 6400.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:115)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs Line: 115)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.356 seconds
Domain Reload Profiling: 1862ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (345ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (134ms)
				TypeCache.ScanAssembly (120ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1356ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1268ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (75ms)
			ProcessInitializeOnLoadAttributes (1110ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 213 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6615 unused Assets / (4.9 MB). Loaded Objects now: 7320.
Memory consumption went from 178.5 MB to 173.7 MB.
Total: 9.533900 ms (FindLiveObjects: 0.535000 ms CreateObjectMapping: 0.487600 ms MarkObjects: 5.770200 ms  DeleteObjects: 2.739800 ms)

========================================================================
Received Import Request.
  Time since last request: 129309.672394 seconds.
  path: Packages/com.justinpbarnett.unity-mcp/Editor/UnityMcpBridge.Editor.asmdef
  artifactKey: Guid(04b0581466993404a8fae14802c2a5a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.justinpbarnett.unity-mcp/Editor/UnityMcpBridge.Editor.asmdef using Guid(04b0581466993404a8fae14802c2a5a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1f6eecadad2e1f5238f9554309188148') in 0.0059782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Packages/com.justinpbarnett.unity-mcp/package.json
  artifactKey: Guid(a2f7ae0675bf4fb478a0a1df7a3f6c64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.justinpbarnett.unity-mcp/package.json using Guid(a2f7ae0675bf4fb478a0a1df7a3f6c64) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72efcda687e11e6fef328d447eb2ed6b') in 0.012269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.331020 seconds.
  path: Packages/com.justinpbarnett.unity-mcp/Editor/UnityMcpBridge.cs
  artifactKey: Guid(1e0fb0e418dd19345a8236c44078972b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.justinpbarnett.unity-mcp/Editor/UnityMcpBridge.cs using Guid(1e0fb0e418dd19345a8236c44078972b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '899ada549edef75dd99362764fc990d3') in 0.0004553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

Client handler error: Unable to read data from the transport connection: An existing connection was forcibly closed by the remote host.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityMcpBridge.Editor.UnityMcpBridge/<HandleClientAsync>d__16:MoveNext () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:233)
System.Runtime.CompilerServices.AsyncMethodBuilderCore/MoveNextRunner:InvokeMoveNext (object)
System.Threading.ExecutionContext:RunInternal (System.Threading.ExecutionContext,System.Threading.ContextCallback,object,bool)
System.Threading.ExecutionContext:Run (System.Threading.ExecutionContext,System.Threading.ContextCallback,object,bool)
System.Runtime.CompilerServices.AsyncMethodBuilderCore/MoveNextRunner:Run ()
System.Threading.Tasks.AwaitTaskContinuation:RunOrScheduleAction (System.Action,bool,System.Threading.Tasks.Task&)
System.Threading.Tasks.Task:FinishContinuations ()
System.Threading.Tasks.Task:FinishStageThree ()
System.Threading.Tasks.Task:FinishStageTwo ()
System.Threading.Tasks.Task:Finish (bool)
System.Threading.Tasks.Task:TrySetException (object)
System.Threading.Tasks.ValueTask`1/ValueTaskSourceAsTask/<>c<int>:<.cctor>b__4_0 (object)
System.Net.Sockets.Socket/AwaitableSocketAsyncEventArgs:InvokeContinuation (System.Action`1<object>,object,bool)
System.Net.Sockets.Socket/AwaitableSocketAsyncEventArgs:OnCompleted (System.Net.Sockets.SocketAsyncEventArgs)
System.Net.Sockets.SocketAsyncEventArgs:Complete_internal ()
System.Net.Sockets.Socket/<>c:<.cctor>b__367_10 (System.IAsyncResult)
System.Net.Sockets.SocketAsyncResult/<>c:<Complete>b__27_0 (object)
System.Threading.QueueUserWorkItemCallback:System.Threading.IThreadPoolWorkItem.ExecuteWorkItem ()
System.Threading.ThreadPoolWorkQueue:Dispatch ()
System.Threading._ThreadPoolWaitCallback:PerformWaitCallback ()

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs Line: 233)

