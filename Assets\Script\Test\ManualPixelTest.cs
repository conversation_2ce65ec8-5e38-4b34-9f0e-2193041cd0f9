using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 手動像素渲染測試，用於驗證系統功能
    /// </summary>
    public class ManualPixelTest : MonoBehaviour
    {
        [Header("Manual Test")]
        [SerializeField] private bool runTestOnStart = true;
        [SerializeField] private int testCanvasSize = 32;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                Invoke(nameof(RunPixelTest), 0.5f);
            }
        }
        
        [ContextMenu("Run Pixel Test")]
        public void RunPixelTest()
        {
            Debug.Log("=== Manual Pixel Test Started ===");
            
            try
            {
                // 測試基本像素繪製
                TestBasicPixelRendering();
                
                // 測試繪製指令
                TestDrawCommands();
                
                // 測試角色創建
                TestCharacterCreation();
                
                Debug.Log("✅ Manual Pixel Test Completed Successfully!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Manual Pixel Test Failed: {e.Message}");
                Debug.LogException(e);
            }
        }
        
        private void TestBasicPixelRendering()
        {
            Debug.Log("🧪 Testing Basic Pixel Rendering...");
            
            // 獲取繪製引擎實例
            PixelDrawEngine engine = PixelDrawEngine.Instance;
            if (engine == null)
            {
                throw new System.Exception("PixelDrawEngine instance is null");
            }
            Debug.Log("✓ PixelDrawEngine instance obtained");
            
            // 創建畫布
            PixelCanvas canvas = engine.CreateCanvas(testCanvasSize, testCanvasSize);
            if (canvas == null)
            {
                throw new System.Exception("Failed to create canvas");
            }
            Debug.Log($"✓ Canvas created ({testCanvasSize}x{testCanvasSize})");
            
            // 設置像素
            canvas.SetPixel(5, 5, Color.red);
            canvas.SetPixel(10, 10, Color.green);
            canvas.SetPixel(15, 15, Color.blue);
            
            // 驗證像素
            Color testColor = canvas.GetPixel(5, 5);
            if (testColor != Color.red)
            {
                throw new System.Exception($"Pixel color mismatch. Expected: {Color.red}, Got: {testColor}");
            }
            Debug.Log("✓ Pixel set/get verification passed");
            
            // 應用變更
            canvas.ApplyChanges();
            Debug.Log("✓ Canvas changes applied");
            
            // 創建測試精靈
            CreateTestSprite(canvas, "BasicTest", Vector3.zero);
            
            // 歸還畫布
            engine.ReturnCanvas(canvas);
            Debug.Log("✓ Canvas returned to pool");
            
            Debug.Log("✅ Basic Pixel Rendering Test Passed");
        }
        
        private void TestDrawCommands()
        {
            Debug.Log("🧪 Testing Draw Commands...");
            
            PixelDrawEngine engine = PixelDrawEngine.Instance;
            PixelCanvas canvas = engine.CreateCanvas(testCanvasSize, testCanvasSize);
            
            // 測試填充圓形指令
            var circleCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { testCanvasSize/2, testCanvasSize/2, 8 }, Color.yellow);
            engine.ExecuteDrawCommand(canvas, circleCommand);
            Debug.Log("✓ FillCircle command executed");
            
            // 測試線條指令
            var lineCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { 0, 0, testCanvasSize-1, testCanvasSize-1 }, Color.red);
            engine.ExecuteDrawCommand(canvas, lineCommand);
            Debug.Log("✓ DrawLine command executed");
            
            // 測試矩形指令
            var rectCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { 2, 2, 6, 4 }, Color.blue);
            engine.ExecuteDrawCommand(canvas, rectCommand);
            Debug.Log("✓ FillRect command executed");
            
            // 應用變更並創建精靈
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "DrawCommandsTest", new Vector3(2, 0, 0));
            
            engine.ReturnCanvas(canvas);
            Debug.Log("✅ Draw Commands Test Passed");
        }
        
        private void TestCharacterCreation()
        {
            Debug.Log("🧪 Testing Character Creation...");
            
            PixelDrawEngine engine = PixelDrawEngine.Instance;
            PixelCanvas canvas = engine.CreateCanvas(testCanvasSize, testCanvasSize);
            
            // 創建史萊姆角色
            CreateSlime(engine, canvas);
            
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "SlimeCharacter", new Vector3(-2, 0, 0));
            
            engine.ReturnCanvas(canvas);
            Debug.Log("✅ Character Creation Test Passed");
        }
        
        private void CreateSlime(PixelDrawEngine engine, PixelCanvas canvas)
        {
            // 史萊姆身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { testCanvasSize/2, testCanvasSize/2 + 2, 10 }, 
                new Color(0.2f, 0.8f, 0.2f, 1f));
            engine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 左眼
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { testCanvasSize/2 - 3, testCanvasSize/2 }, Color.black);
            engine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            // 右眼
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { testCanvasSize/2 + 3, testCanvasSize/2 }, Color.black);
            engine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            // 嘴巴
            var mouthCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { testCanvasSize/2 - 2, testCanvasSize/2 - 3, 
                           testCanvasSize/2 + 2, testCanvasSize/2 - 3 }, Color.black);
            engine.ExecuteDrawCommand(canvas, mouthCommand);
            
            Debug.Log("✓ Slime character created");
        }
        
        private void CreateTestSprite(PixelCanvas canvas, string name, Vector3 position)
        {
            GameObject spriteObj = new GameObject($"Test_{name}");
            spriteObj.transform.position = position;
            
            SpriteRenderer renderer = spriteObj.AddComponent<SpriteRenderer>();
            
            // 創建精靈
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                16f
            );
            
            renderer.sprite = sprite;
            
            Debug.Log($"✓ Test sprite '{name}' created at {position}");
        }
        
        [ContextMenu("Clear Test Sprites")]
        public void ClearTestSprites()
        {
            GameObject[] allObjects = FindObjectsOfType<GameObject>();
            int cleared = 0;
            
            foreach (GameObject obj in allObjects)
            {
                if (obj.name.StartsWith("Test_"))
                {
                    DestroyImmediate(obj);
                    cleared++;
                }
            }
            
            Debug.Log($"Cleared {cleared} test sprites");
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 200, 120));
            
            if (GUILayout.Button("Run Pixel Test"))
            {
                RunPixelTest();
            }
            
            if (GUILayout.Button("Clear Test Sprites"))
            {
                ClearTestSprites();
            }
            
            GUILayout.Label($"Canvas Size: {testCanvasSize}");
            
            GUILayout.EndArea();
        }
    }
}
