{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(\"/mnt/c/Users/<USER>/AppData/Roaming/Python/Python313/Scripts/uv.exe\" --version)", "Bash(ss:*)", "Bash(grep:*)", "Bash(\"/mnt/c/Users/<USER>/AppData/Roaming/Python/Python313/Scripts/uv.exe\" --directory \"/mnt/c/Users/<USER>/AppData/Local/Programs/UnityMCP/UnityMcpServer/src\" run server.py --help)", "<PERSON><PERSON>(python3:*)", "Bash(\"/mnt/c/Users/<USER>/AppData/Local/Programs/UnityMCP/UnityMcpServer/src/.venv/Scripts/python.exe\" \"/mnt/c/Users/<USER>/AppData/Local/Programs/UnityMCP/UnityMcpServer/src/server.py\" --help)"], "deny": []}}