{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 10116, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 10116, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 10116, "tid": 2481, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 10116, "tid": 2481, "ts": 1753920791815523, "dur": 5, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791815536, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 10116, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 10116, "tid": 1, "ts": 1753920791637115, "dur": 1754, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10116, "tid": 1, "ts": 1753920791638872, "dur": 21669, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10116, "tid": 1, "ts": 1753920791660543, "dur": 28595, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791815539, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 10116, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791637095, "dur": 10057, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647153, "dur": 167982, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647160, "dur": 23, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647186, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647346, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647376, "dur": 4, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791647382, "dur": 2526, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791649912, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791649961, "dur": 2, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791649964, "dur": 43, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650009, "dur": 1, "ph": "X", "name": "ProcessMessages 1650", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650012, "dur": 29, "ph": "X", "name": "ReadAsync 1650", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650044, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650046, "dur": 40, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650088, "dur": 1, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650090, "dur": 25, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650118, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650140, "dur": 27, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650170, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650192, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650194, "dur": 23, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650219, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650221, "dur": 21, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650245, "dur": 23, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650270, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650272, "dur": 35, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650308, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650310, "dur": 24, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650337, "dur": 20, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650360, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650380, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650382, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650403, "dur": 20, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650427, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650447, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650472, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650474, "dur": 19, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650495, "dur": 17, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650516, "dur": 20, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650539, "dur": 46, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650587, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650591, "dur": 23, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650616, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650618, "dur": 21, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650642, "dur": 20, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650664, "dur": 17, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650684, "dur": 19, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650706, "dur": 30, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650739, "dur": 15, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650757, "dur": 24, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650784, "dur": 20, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650807, "dur": 32, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650842, "dur": 19, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650863, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650865, "dur": 19, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650886, "dur": 20, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650909, "dur": 15, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650927, "dur": 19, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650947, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650948, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650971, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791650996, "dur": 20, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651019, "dur": 18, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651040, "dur": 21, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651064, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651087, "dur": 20, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651108, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651110, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651137, "dur": 39, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651181, "dur": 20, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651203, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651204, "dur": 20, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651227, "dur": 26, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651256, "dur": 21, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651279, "dur": 18, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651300, "dur": 26, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651329, "dur": 26, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651357, "dur": 20, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651381, "dur": 21, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651404, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651426, "dur": 19, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651448, "dur": 17, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651467, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651489, "dur": 16, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651506, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651508, "dur": 21, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651532, "dur": 20, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651555, "dur": 20, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651578, "dur": 22, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651605, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651607, "dur": 16, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651626, "dur": 46, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651675, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651699, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651721, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651740, "dur": 28, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651772, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651797, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651822, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651823, "dur": 19, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651844, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651846, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651865, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651895, "dur": 20, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651916, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651918, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651940, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651961, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791651981, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652005, "dur": 20, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652027, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652057, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652078, "dur": 30, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652110, "dur": 21, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652134, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652135, "dur": 21, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652158, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652161, "dur": 18, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652181, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652202, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652203, "dur": 64, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652269, "dur": 1, "ph": "X", "name": "ProcessMessages 1538", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652272, "dur": 23, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652297, "dur": 26, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652327, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652347, "dur": 21, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652370, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652397, "dur": 19, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652417, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652419, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652443, "dur": 19, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652465, "dur": 18, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652484, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652486, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652507, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652533, "dur": 19, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652555, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652574, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652599, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652622, "dur": 17, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652641, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652661, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652679, "dur": 20, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652702, "dur": 21, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652725, "dur": 24, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652760, "dur": 21, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652784, "dur": 19, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652806, "dur": 18, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652825, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652827, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652845, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652868, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652916, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652918, "dur": 26, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652946, "dur": 18, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652967, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652986, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791652987, "dur": 21, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653012, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653031, "dur": 16, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653049, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653073, "dur": 25, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653101, "dur": 18, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653121, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653140, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653162, "dur": 18, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653183, "dur": 19, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653205, "dur": 119, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653325, "dur": 1, "ph": "X", "name": "ProcessMessages 2013", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653327, "dur": 18, "ph": "X", "name": "ReadAsync 2013", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653349, "dur": 19, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653370, "dur": 18, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653390, "dur": 17, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653410, "dur": 134, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653547, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653618, "dur": 1, "ph": "X", "name": "ProcessMessages 1944", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653620, "dur": 18, "ph": "X", "name": "ReadAsync 1944", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653642, "dur": 19, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653663, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653686, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653708, "dur": 18, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653728, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653748, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653770, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653790, "dur": 24, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653817, "dur": 19, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653839, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653859, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653878, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653898, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653900, "dur": 20, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653921, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653923, "dur": 17, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653942, "dur": 16, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791653960, "dur": 131, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654094, "dur": 41, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654138, "dur": 2, "ph": "X", "name": "ProcessMessages 3638", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654141, "dur": 33, "ph": "X", "name": "ReadAsync 3638", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654176, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654178, "dur": 24, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654205, "dur": 49, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654256, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654258, "dur": 23, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654284, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654310, "dur": 30, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654342, "dur": 19, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654363, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654387, "dur": 22, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654412, "dur": 19, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654434, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654454, "dur": 21, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654479, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654502, "dur": 23, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654527, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654547, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654571, "dur": 41, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654613, "dur": 1, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654615, "dur": 19, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654637, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654657, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654680, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654700, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654719, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654742, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654744, "dur": 23, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654771, "dur": 18, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654791, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654817, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654819, "dur": 27, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654847, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654849, "dur": 37, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654888, "dur": 17, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654907, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654909, "dur": 21, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654931, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654933, "dur": 32, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654967, "dur": 16, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791654986, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655013, "dur": 25, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655041, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655067, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655069, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655097, "dur": 24, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655122, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655124, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655146, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655169, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655192, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655194, "dur": 26, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655228, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655230, "dur": 20, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655253, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655276, "dur": 21, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655300, "dur": 21, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655322, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655324, "dur": 17, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655343, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655367, "dur": 41, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655412, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655414, "dur": 33, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655449, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655451, "dur": 22, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655476, "dur": 20, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655498, "dur": 37, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655538, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655563, "dur": 21, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655587, "dur": 17, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655608, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655633, "dur": 20, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655665, "dur": 20, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655687, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655688, "dur": 29, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655721, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655741, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655743, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655763, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655783, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655807, "dur": 26, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655836, "dur": 18, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655856, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655878, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655902, "dur": 18, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655923, "dur": 19, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655944, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655966, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791655988, "dur": 19, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656008, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656010, "dur": 17, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656030, "dur": 24, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656056, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656077, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656101, "dur": 25, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656127, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656129, "dur": 26, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656159, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656184, "dur": 40, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656228, "dur": 19, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656250, "dur": 17, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656270, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656289, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656310, "dur": 21, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656334, "dur": 30, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656366, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656368, "dur": 22, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656393, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656413, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656436, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656456, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656476, "dur": 57, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656534, "dur": 1, "ph": "X", "name": "ProcessMessages 1645", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656537, "dur": 30, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656570, "dur": 19, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656591, "dur": 17, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656610, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656629, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656631, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656653, "dur": 17, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656673, "dur": 26, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656701, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656702, "dur": 19, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656723, "dur": 21, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656748, "dur": 23, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656772, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656774, "dur": 19, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656795, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656816, "dur": 48, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656866, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656867, "dur": 20, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656888, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656890, "dur": 22, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656915, "dur": 27, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656944, "dur": 19, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656966, "dur": 16, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791656985, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657005, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657030, "dur": 19, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657052, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657074, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657100, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657122, "dur": 24, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657149, "dur": 16, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657167, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657188, "dur": 29, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657219, "dur": 24, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657246, "dur": 19, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657267, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657268, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657291, "dur": 19, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657312, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657331, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657332, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657352, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657376, "dur": 22, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657401, "dur": 20, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657425, "dur": 17, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657444, "dur": 25, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657472, "dur": 58, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657531, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657533, "dur": 21, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657556, "dur": 23, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657581, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657583, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657604, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657606, "dur": 23, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657632, "dur": 18, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657653, "dur": 18, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657674, "dur": 20, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657697, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657716, "dur": 19, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657739, "dur": 19, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657761, "dur": 24, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657786, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657788, "dur": 18, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657808, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657829, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657830, "dur": 15, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657849, "dur": 51, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657902, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657926, "dur": 16, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657945, "dur": 16, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657963, "dur": 18, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791657984, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658005, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658027, "dur": 17, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658047, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658066, "dur": 22, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658090, "dur": 33, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658126, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658127, "dur": 38, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658168, "dur": 76, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658245, "dur": 1, "ph": "X", "name": "ProcessMessages 1938", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658247, "dur": 18, "ph": "X", "name": "ReadAsync 1938", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658267, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658269, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658289, "dur": 19, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658311, "dur": 17, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658331, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658351, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658372, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658374, "dur": 24, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658399, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658401, "dur": 23, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658427, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658449, "dur": 19, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658472, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658493, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658517, "dur": 19, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658539, "dur": 29, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658570, "dur": 17, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658590, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658609, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658632, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658653, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658654, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658675, "dur": 15, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658693, "dur": 18, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658713, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658753, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658774, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658795, "dur": 59, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658857, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658877, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658899, "dur": 56, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658959, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791658987, "dur": 15, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659005, "dur": 38, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659045, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659069, "dur": 29, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659099, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659101, "dur": 47, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659150, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659171, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659172, "dur": 23, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659198, "dur": 44, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659245, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659276, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659299, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659320, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659361, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659403, "dur": 40, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659446, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659470, "dur": 18, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659491, "dur": 44, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659537, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659558, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659580, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659599, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659646, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659669, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659690, "dur": 43, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659734, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659754, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659756, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659778, "dur": 45, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659825, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791659849, "dur": 252, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660104, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660106, "dur": 49, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660157, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660182, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660184, "dur": 19, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660206, "dur": 260, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660468, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660491, "dur": 22, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660515, "dur": 7, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660523, "dur": 41, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660568, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660594, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660596, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660620, "dur": 47, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660669, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660690, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660692, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660713, "dur": 142, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660858, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660882, "dur": 17, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660901, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660919, "dur": 38, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660958, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660978, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791660998, "dur": 45, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661045, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661065, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661087, "dur": 83, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661173, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661195, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661196, "dur": 239, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661437, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661458, "dur": 38, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661498, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661524, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661525, "dur": 26, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661553, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661574, "dur": 44, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661620, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661641, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661660, "dur": 45, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661708, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661728, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661748, "dur": 45, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661795, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661815, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661835, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661855, "dur": 22, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661880, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661898, "dur": 16, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661916, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661959, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791661986, "dur": 24, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662012, "dur": 44, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662058, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662080, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662101, "dur": 51, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662155, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662180, "dur": 16, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662198, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662272, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662277, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662308, "dur": 1, "ph": "X", "name": "ProcessMessages 1511", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662310, "dur": 26, "ph": "X", "name": "ReadAsync 1511", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662339, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662359, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662379, "dur": 27, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662408, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662429, "dur": 17, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662448, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662450, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662468, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662510, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662529, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662551, "dur": 47, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662600, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662620, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662641, "dur": 18, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662661, "dur": 17, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662681, "dur": 15, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662698, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662716, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662757, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662802, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662830, "dur": 45, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662877, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662897, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662917, "dur": 17, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662937, "dur": 17, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662957, "dur": 15, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662974, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791662992, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663036, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663057, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663078, "dur": 17, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663097, "dur": 36, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663136, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663156, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663159, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663182, "dur": 42, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663226, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663249, "dur": 21, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663273, "dur": 15, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663290, "dur": 34, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663327, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663347, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663369, "dur": 19, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663391, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663412, "dur": 16, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663430, "dur": 17, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663449, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663487, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663507, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663527, "dur": 46, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663574, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663595, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663615, "dur": 43, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663660, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663681, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663708, "dur": 49, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663759, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663779, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663799, "dur": 45, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663846, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663866, "dur": 17, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663885, "dur": 43, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663930, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663931, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663957, "dur": 20, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663979, "dur": 17, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791663998, "dur": 44, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664044, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664077, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664098, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664119, "dur": 48, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664171, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664199, "dur": 1, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664200, "dur": 15, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664218, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664266, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664299, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664301, "dur": 19, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664322, "dur": 29, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664353, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664372, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664373, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664393, "dur": 44, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664439, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664459, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664478, "dur": 14, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664495, "dur": 37, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664534, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664554, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664574, "dur": 52, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664628, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664655, "dur": 20, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664678, "dur": 46, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664726, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664746, "dur": 29, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664777, "dur": 41, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664821, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664845, "dur": 17, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664864, "dur": 14, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664880, "dur": 38, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664920, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664941, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791664962, "dur": 43, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665008, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665028, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665048, "dur": 79, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665133, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665139, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665162, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665165, "dur": 19, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665186, "dur": 17, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665206, "dur": 19, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665227, "dur": 42, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665271, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665299, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665319, "dur": 42, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665364, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665384, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665403, "dur": 52, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665458, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665521, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665523, "dur": 42, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665567, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665588, "dur": 18, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665608, "dur": 44, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665654, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665674, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665697, "dur": 41, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665741, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665761, "dur": 25, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665789, "dur": 14, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665806, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665842, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665862, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791665882, "dur": 116, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666001, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666021, "dur": 18, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666041, "dur": 50, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666094, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666117, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666137, "dur": 44, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666183, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666205, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666227, "dur": 42, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666271, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666292, "dur": 18, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666312, "dur": 65, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666379, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666420, "dur": 4, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666425, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666446, "dur": 27, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666475, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666495, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666515, "dur": 42, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666559, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666579, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666600, "dur": 44, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666646, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666666, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666686, "dur": 50, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666739, "dur": 15, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666757, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666775, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666817, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666845, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666869, "dur": 41, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666912, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666935, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666937, "dur": 22, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666961, "dur": 17, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791666981, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667000, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667020, "dur": 16, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667038, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667091, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667110, "dur": 150, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667265, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667322, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667324, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667406, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667429, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667432, "dur": 37, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667471, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667473, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667496, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667498, "dur": 29, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667530, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667532, "dur": 27, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667563, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667593, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667595, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667634, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667636, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667676, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667678, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667703, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667705, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667754, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667757, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667783, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667786, "dur": 137, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667928, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667961, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667964, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667997, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791667999, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668037, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668039, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668070, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668120, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668121, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668155, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668157, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668178, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668200, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668226, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668247, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668326, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668351, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668352, "dur": 111, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668468, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668497, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668499, "dur": 94, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668596, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668597, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668628, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668648, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668717, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668739, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668740, "dur": 20, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668764, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668788, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668834, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668921, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668923, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668948, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668977, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791668979, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669007, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669029, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669066, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669084, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669149, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669151, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669180, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669181, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669207, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669209, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669238, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669240, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669259, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669261, "dur": 13, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669276, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669303, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669329, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669331, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669349, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669377, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669406, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669408, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669424, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669483, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669508, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669509, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669541, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669564, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669589, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669666, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669686, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669687, "dur": 39, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669732, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669733, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669776, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669778, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669812, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669814, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669836, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669856, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669879, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669880, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669904, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669935, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669936, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791669999, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670034, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670036, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670066, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670092, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670144, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670185, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670187, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670208, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670233, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670252, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670289, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670313, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670330, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670362, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670364, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670385, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670405, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670464, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670493, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670622, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670626, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670671, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670674, "dur": 40, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670716, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670718, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670755, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670756, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670796, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670798, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670839, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670866, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670900, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670902, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670937, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670939, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791670971, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671001, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671003, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671025, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671067, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671107, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671109, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671137, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671139, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671159, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671161, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671178, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671212, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671232, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671234, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671252, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671279, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671281, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671331, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671386, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671388, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671425, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671427, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671467, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671489, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671491, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671515, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671544, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671576, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671578, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671618, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671620, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671642, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671696, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671739, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671741, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671759, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671782, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671804, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671829, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671868, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671870, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671912, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671914, "dur": 17, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671933, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671967, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671968, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791671994, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791672015, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791672044, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791672077, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791672104, "dur": 1574, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791673681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791673684, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791673721, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791673999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674002, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674034, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674036, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674299, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674340, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674342, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674385, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674404, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674541, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674574, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674884, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791674904, "dur": 22451, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697365, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697411, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697446, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697554, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697577, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697817, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697837, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697862, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791697950, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698175, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698215, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698217, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698243, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698245, "dur": 127, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698375, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698411, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698413, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698449, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698480, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698482, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698502, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698609, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698634, "dur": 206, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698844, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698871, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698915, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698946, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698948, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698967, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791698992, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699139, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699160, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699206, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699244, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699278, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699314, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699342, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699376, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699418, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699420, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699456, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699509, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699540, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699542, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699567, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699608, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699643, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699673, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699675, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699708, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699739, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699772, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699814, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699844, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699873, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699916, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699946, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791699981, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700019, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700051, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700087, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700120, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700153, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700155, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700193, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700307, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700338, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700342, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700375, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700377, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700409, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700411, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700444, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700469, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700470, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700516, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700662, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700701, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700737, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700772, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700808, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700846, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700883, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791700970, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701004, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701005, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701088, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701134, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701218, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701237, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701266, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701293, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701294, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701393, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701434, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701436, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701472, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701475, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701523, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701524, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701566, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701605, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701718, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701755, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701785, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701819, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701853, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701895, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701927, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701956, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791701983, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702021, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702044, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702087, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702123, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702151, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702181, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702267, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702286, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702305, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702323, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702352, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702383, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702417, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702456, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702488, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702519, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702520, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702559, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702590, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702680, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702704, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702754, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702783, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702922, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702959, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791702960, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703018, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703052, "dur": 182, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703239, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703276, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703279, "dur": 27, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703308, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703338, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703432, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703434, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703460, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703487, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703523, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703552, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703554, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703618, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703642, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703661, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703680, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703711, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703734, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703788, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703809, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703827, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703940, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791703959, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704056, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704074, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704108, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704231, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704254, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704284, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704375, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704392, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704422, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704423, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704443, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704479, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704508, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704538, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704559, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704640, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704658, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704840, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704870, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704897, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791704903, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705022, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705065, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705067, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705139, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705176, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705178, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705218, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705249, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705286, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705323, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705325, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705474, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705506, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705531, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705582, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705600, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705618, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705636, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705654, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705738, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705756, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705802, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705819, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791705970, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706000, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706082, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706113, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706143, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706226, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706246, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706274, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706294, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706454, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706473, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706490, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706513, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706533, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706569, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706587, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706771, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706789, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791706791, "dur": 36937, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791743736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791743739, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791743781, "dur": 16, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791743799, "dur": 8272, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752080, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752131, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752133, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752233, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752255, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752257, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752306, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752336, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752432, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752452, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752700, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752724, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752792, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791752810, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791753025, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791753048, "dur": 705, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791753757, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791753775, "dur": 525, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754305, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754329, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754389, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754412, "dur": 356, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754774, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754794, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754856, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754885, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754887, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754920, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791754922, "dur": 300, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755225, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755254, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755256, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755325, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755367, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755395, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755440, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755458, "dur": 354, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755818, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791755839, "dur": 327, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791756172, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791756195, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791756335, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791756356, "dur": 634, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791756995, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757035, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757076, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757186, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757215, "dur": 86, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757303, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757337, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757339, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757458, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791757478, "dur": 906, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758390, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758418, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758420, "dur": 523, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758948, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791758970, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759130, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759133, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759153, "dur": 385, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759542, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759559, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759595, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759648, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791759650, "dur": 854, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760508, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760528, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760726, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760749, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760774, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791760776, "dur": 314, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761094, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761128, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761130, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761624, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761657, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761695, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761727, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761730, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761784, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761804, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761826, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761967, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791761988, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762010, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762032, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762058, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762059, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762080, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762103, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762139, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762169, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762171, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762195, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762214, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762238, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762260, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762292, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762326, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762327, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762356, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762358, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762390, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762490, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762527, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762530, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762554, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762575, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762576, "dur": 20, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762598, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762600, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762629, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762665, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762667, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762694, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762696, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762726, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762728, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762752, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762777, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762778, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762831, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791762832, "dur": 279, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763117, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763185, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763186, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763225, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763344, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763372, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763400, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763439, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763508, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763510, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763541, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763671, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763709, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763711, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763782, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763812, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763846, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791763926, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764038, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764040, "dur": 100, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764145, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764187, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764214, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764301, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764321, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764431, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764453, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764481, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764504, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764616, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791764791, "dur": 38538, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791803335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791803338, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791803358, "dur": 13, "ph": "X", "name": "ProcessMessages 3158", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791803372, "dur": 3916, "ph": "X", "name": "ReadAsync 3158", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791807292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791807293, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10116, "tid": 25769803776, "ts": 1753920791807312, "dur": 7814, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791815544, "dur": 1186, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 10116, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 10116, "tid": 21474836480, "ts": 1753920791637070, "dur": 52100, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 10116, "tid": 21474836480, "ts": 1753920791689172, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 10116, "tid": 21474836480, "ts": 1753920791689173, "dur": 50, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791816732, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 10116, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 10116, "tid": 17179869184, "ts": 1753920791635263, "dur": 179910, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 10116, "tid": 17179869184, "ts": 1753920791635331, "dur": 1434, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 10116, "tid": 17179869184, "ts": 1753920791815178, "dur": 56, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 10116, "tid": 17179869184, "ts": 1753920791815187, "dur": 15, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791816737, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753920791647822, "dur": 1298, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791649127, "dur": 705, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791649937, "dur": 81, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753920791650019, "dur": 603, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791660594, "dur": 242, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753920791650639, "dur": 17211, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791667864, "dur": 138972, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791806839, "dur": 330, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791807968, "dur": 52, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791808040, "dur": 1005, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753920791650375, "dur": 17498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791667897, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668040, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_DDB5C51061C17C60.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753920791668110, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668325, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668450, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668506, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_093A9AB2690054D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753920791668659, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668737, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791668956, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791669219, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791669482, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791669733, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791670007, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791670203, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791670425, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791670578, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791670790, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791671036, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791671352, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791671517, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791671743, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791671995, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791672208, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791672372, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791672635, "dur": 3354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791675989, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791676286, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791676634, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791676945, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791677259, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791678046, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791678728, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791682297, "dur": 816, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\Lighting\\ProbeVolume\\ProbeSubdivisionContext.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753920791679148, "dur": 4566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791683714, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791684067, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791684449, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791684794, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791686746, "dur": 5221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791691968, "dur": 5570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791697539, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791698155, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791698687, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753920791699289, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791699359, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753920791700310, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791700415, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791700470, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791701113, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791701256, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791702233, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791703265, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791703553, "dur": 2127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791705680, "dur": 43526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791749207, "dur": 6478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753920791755686, "dur": 2240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791757930, "dur": 4548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753920791762479, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791763336, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791763399, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753920791763522, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791764135, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753920791765038, "dur": 41880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791650703, "dur": 17344, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791668057, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_89670D5C2C45BF09.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753920791668113, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791668241, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791668618, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B18C92BFAF41A304.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753920791668679, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791668818, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791669126, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791669429, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791669656, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791669930, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791670067, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791670285, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791670532, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791670794, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791671047, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791671372, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791671673, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791671917, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791672135, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791672322, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791672608, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791672820, "dur": 4621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791677442, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791678336, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791682408, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\RenderGraph\\RenderGraphViewer.SidePanel.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753920791678875, "dur": 4547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791683422, "dur": 3376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791686799, "dur": 4870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791691669, "dur": 5447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791697116, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791698131, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791698676, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753920791698900, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791698961, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753920791699969, "dur": 628, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791700603, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791700664, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791701116, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791701241, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753920791701436, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791701555, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753920791702129, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791702679, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791703234, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791703542, "dur": 2142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791705684, "dur": 43515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791749201, "dur": 5124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753920791754326, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791755056, "dur": 5150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753920791760207, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791760291, "dur": 3786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753920791764078, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791764255, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753920791764431, "dur": 42390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791650390, "dur": 17495, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791667898, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791668045, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5551594DDB8B09FD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753920791668155, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_E9964D9AA5E80B93.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753920791668213, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791668347, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791668524, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791668732, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791668972, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791669197, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791669467, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791669705, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791669943, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791670087, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791670271, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791670467, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791670680, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791670886, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791671078, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791671429, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791671747, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791671979, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791672136, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791672323, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791672567, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791672827, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791674047, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791674768, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791675144, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791675492, "dur": 2876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791678369, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791680442, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791680721, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791680997, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791681555, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791681901, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791682220, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791682579, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791682934, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791683321, "dur": 3499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791686820, "dur": 5129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791691949, "dur": 5462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791697412, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791698154, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791698744, "dur": 1576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791700368, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791701113, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791701254, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791702026, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753920791702144, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791702215, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753920791702737, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791702864, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791703257, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791703553, "dur": 2126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791705679, "dur": 43564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791749245, "dur": 3981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753920791753227, "dur": 4559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791757792, "dur": 3947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753920791761740, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753920791761840, "dur": 3064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityMcpBridge.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753920791764984, "dur": 41833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791650681, "dur": 17309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668011, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_738E4347AEB012E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753920791668124, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668224, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_39C999403517D369.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753920791668279, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668393, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668504, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668700, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791668849, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791669097, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791669412, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791669714, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791670020, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791670196, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791670413, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791670582, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791670799, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791671057, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791671418, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791671747, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791672011, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791672247, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791672494, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791672723, "dur": 3871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791676595, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791677242, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791678411, "dur": 4134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791682546, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791683263, "dur": 3714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791686978, "dur": 5114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791692093, "dur": 5501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791697594, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791698154, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791698665, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753920791698860, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791698927, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753920791699470, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791700028, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791700337, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791701108, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791701260, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791702236, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791703248, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791703546, "dur": 2129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791705675, "dur": 1211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791706887, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753920791706951, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791707029, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753920791707268, "dur": 41944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791749222, "dur": 6147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753920791755370, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791755516, "dur": 3287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753920791758804, "dur": 4529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791763337, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753920791763543, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791764277, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753920791765370, "dur": 41469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791650435, "dur": 17468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791668022, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_ABA9B3DFDF80AFD9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753920791668107, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791668181, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_18960D046C0BE4AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753920791668300, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791668732, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791668989, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791669273, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791669509, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791669742, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791669996, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791670212, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791670452, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791670722, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791670941, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791671341, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791671683, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791671948, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791672140, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791672335, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791672631, "dur": 3645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791676277, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791676630, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791676958, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791677906, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791678413, "dur": 3145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791681558, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791681953, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791682570, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791683262, "dur": 3889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791687152, "dur": 5710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791692862, "dur": 4837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791697700, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791698158, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791698689, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753920791699124, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791699227, "dur": 1717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753920791700944, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791701139, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791701827, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753920791702221, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753920791702677, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791702778, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791703260, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791703551, "dur": 2130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791705682, "dur": 43563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791749246, "dur": 3799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753920791753046, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791753169, "dur": 4071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753920791757241, "dur": 3093, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791760346, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753920791763239, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791763520, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791763844, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791763899, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753920791764707, "dur": 42116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791650474, "dur": 17630, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791668104, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_A33CBF694AC7400C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753920791668166, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791668246, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F8E3124ECAB7836F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753920791668308, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791668521, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_6855010ACA3EF767.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753920791668687, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791668816, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791669149, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791669396, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791669630, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791669961, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791670173, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791670406, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791670552, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791670724, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791670953, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791671390, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791671674, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791671957, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791672191, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791672361, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791672646, "dur": 2956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791677297, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@996e69f78764\\Editor\\Tasks\\CellTasks.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753920791675602, "dur": 3230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791678833, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791679454, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791679894, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791680192, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791680526, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791680820, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791682381, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Runtime\\GPUDriven\\InstanceData\\InstanceDataSystem.Jobs.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753920791681223, "dur": 4617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791685840, "dur": 4704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791698227, "dur": 148, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1753920791698375, "dur": 1010, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1753920791699385, "dur": 68, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1753920791690544, "dur": 8912, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791699456, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791700324, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791701112, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791701265, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791702212, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753920791702488, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791702562, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753920791703110, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791703239, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791703306, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791703545, "dur": 2140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791705685, "dur": 43566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791749257, "dur": 5635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753920791754893, "dur": 2841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791757743, "dur": 4496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753920791762240, "dur": 1534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791763782, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791764054, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753920791764983, "dur": 41927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791650493, "dur": 17529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791668023, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_DEBDED1949451DE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753920791668125, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791668234, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_13E397B56C28FC84.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753920791668498, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791668721, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A4B06A5D3F17413D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753920791668776, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791669080, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791669264, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791669522, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791669811, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791670080, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791670295, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791670512, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791670721, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791670936, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791671155, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791671471, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753920791671559, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791671772, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791672024, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791672214, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791672397, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791672655, "dur": 3476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791676132, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791676702, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791677126, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791678536, "dur": 3821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791682358, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791683012, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791683610, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791684490, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791684911, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791685340, "dur": 3972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791689312, "dur": 5649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791694961, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791695237, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791695507, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791695791, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791697815, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791698149, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791698682, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753920791699061, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791699147, "dur": 1081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753920791700228, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791700369, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791701123, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791701244, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753920791701632, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791701712, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753920791702392, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791703127, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791703231, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791703543, "dur": 2140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791705683, "dur": 43519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791749203, "dur": 3660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753920791752864, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791752974, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753920791756690, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791756977, "dur": 5482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753920791762460, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791762583, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791762763, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791762864, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791763135, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791763274, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791763555, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753920791764595, "dur": 42225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791650510, "dur": 17584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791668095, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_7772D50AFD20CFAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791668161, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791668303, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791668517, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F0BAD7E6E150FDA3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791668664, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791668796, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791669189, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791669307, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791669563, "dur": 4741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791674305, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791674376, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791674756, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791677374, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\UnitTesting\\TestsCallback.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753920791675150, "dur": 3903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791682299, "dur": 804, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\LookDev\\Compositor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753920791679053, "dur": 4697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791683750, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791684060, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791684445, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791684803, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791685151, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791685424, "dur": 4491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791689915, "dur": 5570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791695485, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791696321, "dur": 1808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791698129, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791698662, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791699009, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791699151, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791700429, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791701194, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791701324, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791701388, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791702269, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791702918, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791703073, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791703450, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791703536, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791703760, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791704093, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791704578, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791704695, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791705075, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791705198, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791705310, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791705546, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791705670, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791705797, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791706059, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791706260, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791706355, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791706761, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753920791706883, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791706989, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791707242, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753920791707324, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753920791707599, "dur": 96459, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753920791650533, "dur": 17560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791668094, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2107026CEB574BDA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753920791668213, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791668354, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_33422ED1CF38A2D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753920791668415, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791668691, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791668821, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791669163, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791669482, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791669747, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791670089, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791670295, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791670501, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791670707, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791670934, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791671203, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791671466, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791671765, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791671986, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791672210, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791672493, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791677449, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.50f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753920791672763, "dur": 5422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791678185, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791682330, "dur": 848, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\ShaderStripping\\ShaderStrippingWatcher.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753920791678873, "dur": 4688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791683562, "dur": 3559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791687121, "dur": 5440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791692561, "dur": 5061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791697622, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791698141, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791698707, "dur": 1624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791700331, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791701118, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791701258, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791702239, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791703253, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791703550, "dur": 2127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791705678, "dur": 43532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791749212, "dur": 5334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753920791754547, "dur": 1055, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791755610, "dur": 4156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753920791759767, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791759882, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753920791764000, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791764185, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753920791765266, "dur": 41558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791650551, "dur": 17529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791668081, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_9FEA77D363FB2657.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753920791668163, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791668315, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791668696, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_6CB9AFB0DBFF6DBE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753920791668755, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791668879, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791669142, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791669398, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791669621, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791669854, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753920791669991, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791670142, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791670323, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791670556, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791670776, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791670984, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791671320, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791671497, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753920791671574, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791671817, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791672090, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791672240, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791672404, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791672691, "dur": 2606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791675306, "dur": 3256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791678563, "dur": 3833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791682396, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791683113, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791683680, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791684251, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791684750, "dur": 3309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791688059, "dur": 5491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791693550, "dur": 4244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791697794, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791698136, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791698675, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753920791698886, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791698957, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791699942, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791700262, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791700332, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791701110, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791701247, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753920791701477, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791701719, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791702184, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791702277, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791703229, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791703540, "dur": 1619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791705161, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753920791705303, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791705397, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791705796, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791705895, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791705961, "dur": 43277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791749240, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791751625, "dur": 4011, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791755640, "dur": 3974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791759615, "dur": 1884, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791761505, "dur": 3067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753920791764573, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753920791764705, "dur": 42102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791650805, "dur": 17191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791667999, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_26C7BC0FFC7E97F9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753920791668091, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791668279, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7F0BD4B4E2440951.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753920791668337, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791668476, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791668692, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_200B73A1C996B8A4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753920791668746, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791668982, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791669195, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791669442, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791669691, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791669955, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791670212, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791670490, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791670700, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791670919, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791671155, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791671435, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753920791671608, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791671867, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791672112, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791672359, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791672671, "dur": 2719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791675390, "dur": 3191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791678581, "dur": 3947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791682529, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791683176, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791683510, "dur": 3421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791686931, "dur": 5291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791692223, "dur": 5263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791697486, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791698132, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791698689, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753920791699072, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791699157, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753920791700146, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791700285, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791700353, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791701134, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791701272, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791702230, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791703241, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791703541, "dur": 2145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791705687, "dur": 47161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791752851, "dur": 4834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753920791757686, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791757857, "dur": 4452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753920791762310, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791762446, "dur": 2748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753920791765195, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753920791765309, "dur": 41525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791650586, "dur": 17492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668079, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_CD3DFCB8C7B55F7B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753920791668201, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668283, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_24B7ED3F1C74699F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753920791668339, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668449, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668528, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668664, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C8D5F263790CB418.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753920791668720, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791668990, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791669269, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791669506, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791669825, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753920791669989, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791670210, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791670458, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791670646, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791670818, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791671042, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791671380, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791671665, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791671884, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791672139, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791672347, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791672605, "dur": 3471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791676076, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791676366, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791676698, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791676995, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791677300, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791677955, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791678649, "dur": 2312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791680961, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791681413, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791682030, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791682670, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791683311, "dur": 3488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791686800, "dur": 4969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791691769, "dur": 5544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791697314, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791698140, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791698673, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753920791699023, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791699119, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753920791700036, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791700580, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791700681, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753920791700838, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791700902, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753920791701440, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791701547, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791701609, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791702226, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791703225, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791703548, "dur": 2144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791705692, "dur": 43579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791749272, "dur": 5128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753920791754401, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791754505, "dur": 3480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753920791757986, "dur": 3514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753920791761506, "dur": 3102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753920791764664, "dur": 42145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791650865, "dur": 17095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791667961, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_F7C930BE34D66A17.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753920791668207, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791668300, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791668625, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_3D9F916F2E9E0052.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753920791668721, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791668867, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_ADBA48EC5D69751F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753920791668919, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791669169, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791669446, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791669734, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791670001, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791670163, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791670431, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791670664, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791670884, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791671106, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791671474, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityMcpBridge.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753920791671567, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791671831, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791672098, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791672275, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791672557, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791677244, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\ProjectGeneration\\ProjectProperties.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753920791672808, "dur": 5042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791677851, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791678368, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791678853, "dur": 2916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791681770, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791682099, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791682449, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791682802, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791683188, "dur": 3391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791686579, "dur": 4841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791691421, "dur": 5895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791697317, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791698151, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791698681, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753920791699294, "dur": 1097, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791700396, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791700960, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791701113, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791701182, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791701255, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791702220, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791703228, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791703544, "dur": 2130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791705674, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791706265, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753920791706342, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791706398, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791706734, "dur": 42494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791749229, "dur": 3321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791752551, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791753058, "dur": 4819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791757878, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791758059, "dur": 4181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791762242, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753920791762453, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753920791765252, "dur": 41656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791650634, "dur": 17395, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668029, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_DB9E1AA76F1F92D1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791668095, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668205, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668300, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668452, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_67DF0CCEE81815F6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791668516, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668622, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_2D28A4C05B0759DF.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791668691, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791668791, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791669107, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791669390, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791669618, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791669860, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753920791669994, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791670202, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791670457, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791670617, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791670808, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791671052, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791671362, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791671517, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753920791671586, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791671806, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791672065, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791672239, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791672490, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791672695, "dur": 3067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791675762, "dur": 2631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791678394, "dur": 2690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791681084, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791681738, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791682116, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791682464, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791682832, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791683226, "dur": 3472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791686698, "dur": 4988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791691687, "dur": 5692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791697380, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791698126, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791698666, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791699167, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791699362, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791700301, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791700519, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791701111, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791701271, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791701504, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791701574, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791702238, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791702332, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791703235, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791703539, "dur": 1315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791704855, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753920791704945, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791705009, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791705800, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791705949, "dur": 43397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791749348, "dur": 5032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791754381, "dur": 1682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791756075, "dur": 5244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791761320, "dur": 1046, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753920791762377, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753920791765220, "dur": 41696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791650652, "dur": 17424, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791668077, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4D77E4C00F04FF80.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753920791668150, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791668276, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791668497, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791668636, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_19797E7D1337D0B5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753920791668799, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791669086, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791669364, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791669584, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791669843, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753920791669919, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753920791669970, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791670177, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791670418, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791670581, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791670816, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791671078, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791671390, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753920791671670, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791672006, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791672210, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791672366, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791672643, "dur": 2950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791677291, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.pixel-perfect@e3ae982b672d\\Editor\\Converter\\U2DToURPPixelPerfectConverter.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753920791675594, "dur": 3066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791678661, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791682283, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\Camera\\CameraUI.PhysicalCamera.Skin.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753920791679315, "dur": 4626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791683941, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791684646, "dur": 3306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791687952, "dur": 5640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791693592, "dur": 4228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791697821, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791698144, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791698664, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753920791698868, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791698959, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753920791699928, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791700158, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791700354, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791701131, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791701275, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791702222, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791703268, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791703574, "dur": 2124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791705698, "dur": 43521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791749225, "dur": 5991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753920791755217, "dur": 752, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791755980, "dur": 4577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753920791760558, "dur": 2637, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791763203, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791763317, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791763477, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791764033, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753920791764988, "dur": 41923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791650413, "dur": 17481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791667900, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_9A23DB704F67602B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753920791668060, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791668235, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791668363, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791668494, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791668638, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_E1AB50F8B64473D7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753920791668723, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791668806, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753920791668884, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791669139, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791669379, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791669611, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791669857, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753920791669981, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753920791670049, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791670290, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791670502, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791670699, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791670892, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791671093, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791671396, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791671684, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791671982, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791672160, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791672333, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791672553, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791672804, "dur": 4193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791676998, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791677337, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791678048, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791678736, "dur": 2190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791680926, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791682375, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Runtime\\GPUDriven\\BatchLayers.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753920791681256, "dur": 2634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791683891, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791684235, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791684571, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791686382, "dur": 4650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791691032, "dur": 5838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791696871, "dur": 1259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791698131, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791698684, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753920791698839, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791698922, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753920791699555, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791699666, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791699734, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791700325, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791701134, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791701277, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791702216, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753920791702451, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791702537, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753920791703064, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791703171, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791703233, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791703551, "dur": 2137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791705688, "dur": 43515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791749205, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753920791752646, "dur": 2996, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791755647, "dur": 4469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753920791760117, "dur": 1354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791761484, "dur": 3368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753920791764853, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753920791764995, "dur": 41863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791650695, "dur": 17313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791668009, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_4F4DC17B3AA5468E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753920791668207, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791668312, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791668530, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791668688, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6E5B87AE1DD63740.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753920791668743, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791668960, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791669200, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791669461, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791669699, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791669946, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791670140, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791670330, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791670533, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791670722, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791670926, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791671099, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791671405, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791671738, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791671997, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791672203, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791672400, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791672652, "dur": 2918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791675570, "dur": 4046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791679617, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791679901, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791680180, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791680505, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791680782, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791682271, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Runtime\\GPUDriven\\OcclusionCullingDebugShaderVariables.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753920791681090, "dur": 2829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791683919, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791684531, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791685179, "dur": 3620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791688800, "dur": 5398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791694198, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791694448, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791694622, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791694807, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791695058, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791695322, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791695598, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791695873, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791697805, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791698138, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791698700, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753920791698840, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791698929, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791700002, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791700123, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753920791700448, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791700561, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791701031, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791701133, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791701269, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791702214, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791702581, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791702649, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791702722, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791703238, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791703563, "dur": 2123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791705686, "dur": 43522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791749210, "dur": 3787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791752998, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791753543, "dur": 3449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791756993, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791757077, "dur": 2937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753920791760015, "dur": 2729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791762840, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791762958, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791763112, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791763496, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791763558, "dur": 1100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753920791764678, "dur": 42134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791650451, "dur": 17475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791667927, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_F0449921BBE59DDA.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791668126, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791668240, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791668529, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_4B4A74992F2593D4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791668720, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791668837, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791669088, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791669268, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791669508, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791669754, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791670080, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791670289, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791670470, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791670683, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791670879, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791671069, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791671369, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791671653, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791671880, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791672112, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791672279, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791672500, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791672749, "dur": 3333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791676082, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791676388, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791676703, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791677009, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791677325, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791678046, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791678570, "dur": 3549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791682119, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791682490, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791682871, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791683337, "dur": 3586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791686923, "dur": 5263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791692186, "dur": 5229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791697415, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791698123, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791698669, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityMcpBridge.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791698874, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791698972, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityMcpBridge.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791700049, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791700164, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityMcpBridge.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791700325, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityMcpBridge.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791700676, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791700762, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791701136, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791701257, "dur": 960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791702217, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791702694, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791703228, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791703549, "dur": 2121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791705677, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791705880, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791706281, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791706370, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753920791706493, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791706848, "dur": 42400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791749249, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityMcpBridge.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791752878, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791752985, "dur": 4180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791757166, "dur": 1025, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791758202, "dur": 5818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753920791764021, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753920791764590, "dur": 42229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791650717, "dur": 17280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791667998, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_FC619CFD8B5478B3.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753920791668170, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791668259, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791668755, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791668895, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791669162, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791669409, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791669630, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791669852, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1753920791670010, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791670182, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791670418, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791670563, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791670752, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791670966, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791671345, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791671514, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753920791671582, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791671812, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791672039, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791672240, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791672463, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791672694, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791675221, "dur": 3008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791678229, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791682453, "dur": 804, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Runtime\\BatchedDeformation\\DeformationManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753920791678744, "dur": 4513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791683258, "dur": 3235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791686494, "dur": 4908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791691402, "dur": 5588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791696990, "dur": 1137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791698127, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791698668, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753920791699071, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791699589, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753920791700338, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791700433, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791700489, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753920791700655, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791700715, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753920791701258, "dur": 942, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791702226, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791702287, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791703230, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791703547, "dur": 2136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791705683, "dur": 43533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791749216, "dur": 3694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753920791752911, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791753433, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753920791756972, "dur": 2715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791759697, "dur": 4581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753920791764279, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753920791764998, "dur": 41922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791650734, "dur": 17299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791668036, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_64EC96CFCCFBD9D0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791668108, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791668304, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791668443, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_389FF8CBAFE21E4A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791668495, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791668701, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791668861, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791669062, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791669243, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791669310, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791669572, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791669815, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670062, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670239, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670459, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670606, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670794, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791670976, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791671344, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791671531, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753920791671582, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791671793, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791672024, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791672258, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791672450, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791672713, "dur": 2802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791675515, "dur": 3004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791678520, "dur": 2975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791681496, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791681860, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791682172, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791682517, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791682886, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791683267, "dur": 3512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791686780, "dur": 4895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791691676, "dur": 5585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791697261, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791698135, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791698682, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791698904, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791699655, "dur": 1018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791700674, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791700820, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791700896, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791701067, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791701133, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791702453, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791702775, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791702850, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791703244, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791703556, "dur": 2116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791705673, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791705820, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791705902, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791706258, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791706388, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753920791706558, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791706908, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791707027, "dur": 42202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791749236, "dur": 3484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791752721, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791752809, "dur": 3945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753920791756755, "dur": 6069, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791762931, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753920791763105, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791763553, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791764473, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753920791765328, "dur": 41499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791650753, "dur": 17228, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791667983, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_91FAF86C1CC2E178.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791668060, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791668201, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_7F7E69FF4628C391.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791668275, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791668377, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3E9F9AC430E09601.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791668428, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791668533, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791668628, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_D0FDB3A3EE08F5EA.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791668750, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791669130, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791669226, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791669493, "dur": 5482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791675042, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791675306, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791675414, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791675622, "dur": 22398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791698189, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791698299, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791698669, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791698923, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791699899, "dur": 1221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791701135, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791701243, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791701464, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791701882, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791701992, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791702212, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791702457, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791703339, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791703430, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791703549, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791703681, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791704056, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791704295, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791704408, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791704737, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791704849, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1753920791704971, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791705263, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791705695, "dur": 43551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791749248, "dur": 5815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791755064, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791755140, "dur": 3697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791758838, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791759049, "dur": 4596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1753920791763646, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1753920791764628, "dur": 42195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791650770, "dur": 17253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791668024, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C5F02F4EB53EF8C7.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791668092, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791668225, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791668728, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791668830, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791669092, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791669307, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791669599, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791669830, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753920791669907, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791670063, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791670251, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1753920791670389, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791670573, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791670784, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791670988, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791671352, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791671655, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791671861, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791672074, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791672311, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791672604, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791673748, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791674055, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791674337, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791674802, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791675146, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791675437, "dur": 2912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791678350, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791682403, "dur": 792, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\MaterialUpgrader.cs"}}, {"pid": 12345, "tid": 22, "ts": 1753920791678893, "dur": 4503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791683396, "dur": 3574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791686970, "dur": 5047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791692017, "dur": 5504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791697521, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791698146, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791698737, "dur": 1591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791700328, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791701108, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791701254, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791701392, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791701571, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791701965, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791702127, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791702219, "dur": 884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791703104, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791703242, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791704268, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791704371, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791704424, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791704557, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791705054, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791705154, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791705272, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791705617, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1753920791705750, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791706044, "dur": 43182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791749227, "dur": 4020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791753248, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791753771, "dur": 4041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791757813, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791757903, "dur": 4514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1753920791762417, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791762787, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791762984, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791763223, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791763403, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791763467, "dur": 1781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1753920791765250, "dur": 41665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791650785, "dur": 17208, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791667996, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_D0DAC8AF3F725E06.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753920791668117, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791668235, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791668486, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791668755, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791668939, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791669176, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791669499, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791669758, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791670041, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791670214, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791670447, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791670660, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791670907, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791671112, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791671453, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1753920791671578, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791671862, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791672085, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791672238, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791672448, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791672700, "dur": 2783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791677380, "dur": 786, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@676bae148e11\\Editor\\UTK\\Tooltips.cs"}}, {"pid": 12345, "tid": 23, "ts": 1753920791675484, "dur": 3728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791682290, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\Deprecated.cs"}}, {"pid": 12345, "tid": 23, "ts": 1753920791679212, "dur": 4561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791683773, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791684126, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791684477, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791684831, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791685163, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791685444, "dur": 4096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791689540, "dur": 5520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791695060, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791695343, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791695625, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791695910, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791696755, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791698128, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791698663, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753920791699156, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791699218, "dur": 1830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791701048, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791701251, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753920791701428, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791702951, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791703101, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753920791703232, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791703915, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791704069, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1753920791704177, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791704466, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791705708, "dur": 43525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791749272, "dur": 5094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791754367, "dur": 1729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791756106, "dur": 4616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791760723, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791761249, "dur": 3063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1753920791764313, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791764382, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1753920791764435, "dur": 42379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791650565, "dur": 17509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791668085, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6B4D64C75CC89CBD.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753920791668153, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791668256, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791668661, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791668772, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791668868, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791669076, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791669296, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791669531, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791669822, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791670092, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791670296, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791670566, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791670771, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791670988, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791671347, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791671632, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791671934, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791672139, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791672331, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791672566, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791672796, "dur": 2942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791677375, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@996e69f78764\\Editor\\Aseprite\\FrameData.cs"}}, {"pid": 12345, "tid": 24, "ts": 1753920791675738, "dur": 3085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791678824, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791679404, "dur": 3052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791682457, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791682810, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791683346, "dur": 3384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791686730, "dur": 4965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791691695, "dur": 5615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791697310, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791698133, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791698656, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753920791699808, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791699893, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753920791700592, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791700721, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753920791700855, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753920791701217, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791701965, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791702022, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753920791702145, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791702211, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1753920791702443, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791702530, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1753920791703063, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791703231, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1753920791703621, "dur": 133, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791703995, "dur": 40475, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1753920791749202, "dur": 3674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753920791752877, "dur": 3303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791756188, "dur": 3023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1753920791759211, "dur": 3689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791762940, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791763006, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791763145, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791763548, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791764388, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1753920791764503, "dur": 42312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791650835, "dur": 17191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791668026, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0228B3EA2C68189D.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791668082, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791668152, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C4A064031D390915.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791668521, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_782CE50BE13DF5D8.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791668741, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791668931, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791669232, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791669486, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791669741, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791670062, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791670256, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791670526, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791670749, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791670944, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791671228, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791671457, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1753920791671624, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791671926, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791672108, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791672291, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791672511, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791672788, "dur": 4539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791677455, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Nodes\\UV\\PolarCoordinatesNode.cs"}}, {"pid": 12345, "tid": 25, "ts": 1753920791677328, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791678401, "dur": 3739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791682141, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791682869, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791683523, "dur": 3412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791686936, "dur": 4966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791691902, "dur": 5564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791697466, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791698137, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791698660, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791698828, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791698918, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1753920791699653, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791699728, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791700088, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791700323, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791701109, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791701243, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791701541, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791701622, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1753920791702187, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791702717, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791702773, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791703235, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791703535, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1753920791703708, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791703772, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1753920791704152, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791704268, "dur": 1426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791705694, "dur": 43648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791749350, "dur": 5730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1753920791755081, "dur": 1761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791756971, "dur": 3465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1753920791760436, "dur": 2098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791762540, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791763526, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791764150, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791764207, "dur": 1033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1753920791765269, "dur": 41562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791650617, "dur": 17407, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791668069, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791668154, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_60A6C9412F089B55.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1753920791668214, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791668629, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AEEC8DF42D0A2679.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1753920791668704, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791668811, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791669081, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791669278, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791669501, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791669738, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791669955, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791670130, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791670416, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791670621, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791670817, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791671036, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791671335, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791671528, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1753920791671584, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791671853, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791672117, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791672320, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791672600, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791672910, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791673193, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791673467, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791673752, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791674060, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791674355, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791674840, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791677459, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@4d374c7eb6db\\Rider\\Editor\\ProjectGeneration\\LastWriteTracker.cs"}}, {"pid": 12345, "tid": 26, "ts": 1753920791675160, "dur": 3464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791678624, "dur": 3343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791681968, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791682647, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791683278, "dur": 3501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791686779, "dur": 5164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791691944, "dur": 5342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791697286, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791698125, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791698686, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1753920791698877, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791698954, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1753920791699708, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791700322, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791700724, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1753920791700881, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1753920791701193, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791701518, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791702007, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791702227, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791703251, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791703565, "dur": 2134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791705699, "dur": 43576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791749276, "dur": 6073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1753920791755350, "dur": 6115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791761479, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1753920791764402, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1753920791764479, "dur": 42332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791650881, "dur": 17083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791668007, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_3EB2D6BFB14CFBE2.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1753920791668081, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791668166, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_0363DC6893EA1A7B.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1753920791668226, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791668388, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791668703, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791668785, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791669070, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791669299, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791669640, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791669921, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791670095, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791670321, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791670552, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791670765, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791670999, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791671348, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791671570, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791671798, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791672100, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791672252, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791672481, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791672701, "dur": 4022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791676724, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791677088, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791677436, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791678271, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791678777, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791679665, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791679948, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791680230, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791680996, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791681290, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791681568, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791681918, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791682237, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791682577, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791682938, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791683316, "dur": 3606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791686922, "dur": 5175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791692098, "dur": 5452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791697550, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791698146, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791698703, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1753920791699817, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791699947, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791700441, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791701057, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791701119, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791701253, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1753920791701441, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791701851, "dur": 762, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791702614, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791703261, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791703534, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1753920791703663, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791704017, "dur": 1659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791705677, "dur": 43537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791749215, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791752527, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791752970, "dur": 3480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791756451, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791756565, "dur": 4648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1753920791761213, "dur": 1531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791762753, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791762844, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791763179, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791763313, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791763557, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1753920791764633, "dur": 42184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791650897, "dur": 17044, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791667942, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_407069ABDCD04775.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1753920791668147, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791668293, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791668399, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791668641, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3547B5AB6CE6C189.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1753920791668701, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791668830, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791669143, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791669387, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791669613, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791669839, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1753920791669936, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791670105, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791670323, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791670513, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791670714, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791670911, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791671321, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791671475, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1753920791671576, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791671759, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791672029, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791672239, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791672398, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791672656, "dur": 3570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791676227, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791676832, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791677427, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791678301, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791678801, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791682218, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\Lighting\\LightAnchorEditorTool.cs"}}, {"pid": 12345, "tid": 28, "ts": 1753920791679206, "dur": 4479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791683685, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791683993, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791684325, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791684735, "dur": 3329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791688065, "dur": 5664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791695800, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Runtime\\BurstRuntime.cs"}}, {"pid": 12345, "tid": 28, "ts": 1753920791693729, "dur": 3718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791697447, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791698139, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791698704, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791699460, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791700326, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791701107, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791701252, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1753920791701550, "dur": 729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791702283, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1753920791702823, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791702931, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791703023, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791703233, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791703538, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1753920791703690, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791703764, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1753920791704263, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791704372, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791704429, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791705699, "dur": 43542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791749245, "dur": 5395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1753920791754641, "dur": 4405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791759047, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1753920791759138, "dur": 3407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1753920791762546, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791762996, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791763057, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791763352, "dur": 1683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1753920791765037, "dur": 41795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753920791813720, "dur": 1802, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 10116, "tid": 2481, "ts": 1753920791816770, "dur": 179, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 10116, "tid": 2481, "ts": 1753920791816997, "dur": 3827, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 10116, "tid": 2481, "ts": 1753920791815530, "dur": 5317, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}