using UnityEngine;
using PetingGame.Rendering;

namespace PetingGame.Test
{
    /// <summary>
    /// 簡單的空場景像素渲染測試 - 無編譯錯誤版本
    /// </summary>
    public class SimpleEmptySceneTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool autoStartTest = true;
        [SerializeField] private int canvasSize = 64;
        
        [Header("Test Status")]
        [SerializeField] private bool testCompleted = false;
        [SerializeField] private bool testPassed = false;
        [SerializeField] private string currentStatus = "Ready";
        
        private PixelPerfectManager pixelPerfectManager;
        private PixelDrawEngine drawEngine;
        private int testStep = 0;
        
        private void Start()
        {
            Debug.Log("=== Simple Empty Scene Pixel Test Started ===");
            
            if (autoStartTest)
            {
                Invoke(nameof(StartTest), 1f);
            }
        }
        
        [ContextMenu("Start Test")]
        public void StartTest()
        {
            testCompleted = false;
            testPassed = false;
            testStep = 0;
            currentStatus = "Starting...";
            
            InvokeRepeating(nameof(RunNextTestStep), 0.5f, 1f);
        }
        
        private void RunNextTestStep()
        {
            try
            {
                switch (testStep)
                {
                    case 0:
                        InitializeSystem();
                        break;
                    case 1:
                        TestBasicPixelDrawing();
                        break;
                    case 2:
                        TestDrawCommands();
                        break;
                    case 3:
                        TestCharacterCreation();
                        break;
                    case 4:
                        TestPerformance();
                        break;
                    case 5:
                        CompleteTest();
                        return;
                    default:
                        CompleteTest();
                        return;
                }
                
                testStep++;
            }
            catch (System.Exception e)
            {
                FailTest(e.Message);
                Debug.LogException(e);
            }
        }
        
        private void InitializeSystem()
        {
            Debug.Log("🔧 Step 1: Initializing Pixel Rendering System...");
            currentStatus = "Initializing System...";
            
            // 確保有PixelPerfectManager
            pixelPerfectManager = PixelPerfectManager.Instance;
            if (pixelPerfectManager == null)
            {
                GameObject managerObj = GameObject.Find("PixelPerfectManager");
                if (managerObj == null)
                {
                    managerObj = new GameObject("PixelPerfectManager");
                }
                pixelPerfectManager = managerObj.GetComponent<PixelPerfectManager>();
                if (pixelPerfectManager == null)
                {
                    pixelPerfectManager = managerObj.AddComponent<PixelPerfectManager>();
                }
                Debug.Log("✓ PixelPerfectManager created");
            }
            else
            {
                Debug.Log("✓ PixelPerfectManager found");
            }
            
            // 獲取PixelDrawEngine
            drawEngine = PixelDrawEngine.Instance;
            if (drawEngine == null)
            {
                throw new System.Exception("Failed to get PixelDrawEngine instance");
            }
            Debug.Log("✓ PixelDrawEngine instance obtained");
            
            // 驗證系統
            float pixelSize = pixelPerfectManager.GetPixelSize();
            int scaleFactor = pixelPerfectManager.GetScaleFactor();
            Vector2 baseResolution = pixelPerfectManager.GetBaseResolution();
            
            Debug.Log($"✓ System initialized - Pixel Size: {pixelSize}, Scale Factor: {scaleFactor}, Base Resolution: {baseResolution}");
        }
        
        private void TestBasicPixelDrawing()
        {
            Debug.Log("🎨 Step 2: Testing Basic Pixel Drawing...");
            currentStatus = "Testing Basic Drawing...";
            
            PixelCanvas canvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            if (canvas == null)
            {
                throw new System.Exception("Failed to create canvas");
            }
            Debug.Log($"✓ Canvas created ({canvasSize}x{canvasSize})");
            
            // 設置像素
            canvas.SetPixel(10, 10, Color.red);
            canvas.SetPixel(20, 20, Color.green);
            canvas.SetPixel(30, 30, Color.blue);
            canvas.SetPixel(40, 40, Color.yellow);
            
            // 驗證像素
            Color testColor = canvas.GetPixel(10, 10);
            if (testColor != Color.red)
            {
                throw new System.Exception($"Pixel verification failed. Expected: {Color.red}, Got: {testColor}");
            }
            Debug.Log("✓ Pixel set/get verification passed");
            
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "BasicPixelTest", new Vector3(-3, 2, 0));
            
            drawEngine.ReturnCanvas(canvas);
            Debug.Log("✓ Basic pixel drawing test completed");
        }
        
        private void TestDrawCommands()
        {
            Debug.Log("🖌️ Step 3: Testing Draw Commands...");
            currentStatus = "Testing Draw Commands...";
            
            PixelCanvas canvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            
            // 填充圓形
            var circleCmd = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { canvasSize/2, canvasSize/2, 15 }, Color.cyan);
            drawEngine.ExecuteDrawCommand(canvas, circleCmd);
            Debug.Log("✓ FillCircle command executed");
            
            // 繪製線條
            var lineCmd = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { 5, 5, canvasSize-5, canvasSize-5 }, Color.red);
            drawEngine.ExecuteDrawCommand(canvas, lineCmd);
            Debug.Log("✓ DrawLine command executed");
            
            // 填充矩形
            var rectCmd = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { 5, canvasSize-15, 10, 8 }, Color.blue);
            drawEngine.ExecuteDrawCommand(canvas, rectCmd);
            Debug.Log("✓ FillRect command executed");
            
            canvas.ApplyChanges();
            CreateTestSprite(canvas, "DrawCommandsTest", new Vector3(0, 2, 0));
            
            drawEngine.ReturnCanvas(canvas);
            Debug.Log("✓ Draw commands test completed");
        }
        
        private void TestCharacterCreation()
        {
            Debug.Log("👾 Step 4: Testing Character Creation...");
            currentStatus = "Creating Characters...";
            
            // 創建史萊姆角色
            PixelCanvas slimeCanvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            CreateSlimeCharacter(slimeCanvas);
            slimeCanvas.ApplyChanges();
            CreateTestSprite(slimeCanvas, "SlimeCharacter", new Vector3(3, 2, 0));
            drawEngine.ReturnCanvas(slimeCanvas);
            Debug.Log("✓ Slime character created");
            
            // 創建騎士角色
            PixelCanvas knightCanvas = drawEngine.CreateCanvas(canvasSize, canvasSize);
            CreateKnightCharacter(knightCanvas);
            knightCanvas.ApplyChanges();
            CreateTestSprite(knightCanvas, "KnightCharacter", new Vector3(-3, -2, 0));
            drawEngine.ReturnCanvas(knightCanvas);
            Debug.Log("✓ Knight character created");
            
            Debug.Log("✓ Character creation test completed");
        }
        
        private void TestPerformance()
        {
            Debug.Log("⚡ Step 5: Testing Performance...");
            currentStatus = "Testing Performance...";
            
            float startTime = Time.realtimeSinceStartup;
            
            // 創建多個小畫布測試性能
            for (int i = 0; i < 10; i++)
            {
                PixelCanvas canvas = drawEngine.CreateCanvas(16, 16);
                
                var command = new PixelDrawCommand(
                    PixelDrawCommandType.FillCircle, 0, -1,
                    new int[] { 8, 8, 6 }, Color.HSVToRGB(i / 10f, 1f, 1f));
                
                drawEngine.ExecuteDrawCommand(canvas, command);
                canvas.ApplyChanges();
                
                drawEngine.ReturnCanvas(canvas);
            }
            
            float endTime = Time.realtimeSinceStartup;
            float totalTime = endTime - startTime;
            
            Debug.Log($"✓ Performance test completed - 10 canvases processed in {totalTime:F3} seconds");
            
            if (totalTime > 0.1f)
            {
                Debug.LogWarning($"Performance warning: Processing took {totalTime:F3}s (expected < 0.1s)");
            }
        }
        
        private void CreateSlimeCharacter(PixelCanvas canvas)
        {
            // 史萊姆身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillCircle, 0, -1,
                new int[] { canvasSize/2, canvasSize/2 + 4, 18 }, 
                new Color(0.2f, 0.8f, 0.2f, 1f));
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 眼睛
            var leftEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { canvasSize/2 - 6, canvasSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, leftEyeCommand);
            
            var rightEyeCommand = new PixelDrawCommand(
                PixelDrawCommandType.SetPixel, 1, -1,
                new int[] { canvasSize/2 + 6, canvasSize/2 + 2 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, rightEyeCommand);
            
            // 嘴巴
            var mouthCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { canvasSize/2 - 4, canvasSize/2 - 4, canvasSize/2 + 4, canvasSize/2 - 4 }, Color.black);
            drawEngine.ExecuteDrawCommand(canvas, mouthCommand);
        }
        
        private void CreateKnightCharacter(PixelCanvas canvas)
        {
            // 身體
            var bodyCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { canvasSize/2 - 8, canvasSize/2 + 4, 16, 20 }, Color.blue);
            drawEngine.ExecuteDrawCommand(canvas, bodyCommand);
            
            // 頭盔
            var helmetCommand = new PixelDrawCommand(
                PixelDrawCommandType.FillRect, 0, -1,
                new int[] { canvasSize/2 - 6, canvasSize/2 - 8, 12, 12 }, Color.gray);
            drawEngine.ExecuteDrawCommand(canvas, helmetCommand);
            
            // 劍
            var swordCommand = new PixelDrawCommand(
                PixelDrawCommandType.DrawLine, 1, -1,
                new int[] { canvasSize/2 + 12, canvasSize/2 - 4, canvasSize/2 + 12, canvasSize/2 + 16 }, Color.white);
            drawEngine.ExecuteDrawCommand(canvas, swordCommand);
        }
        
        private void CreateTestSprite(PixelCanvas canvas, string name, Vector3 position)
        {
            GameObject spriteObj = new GameObject($"Test_{name}");
            spriteObj.transform.position = position;
            
            SpriteRenderer renderer = spriteObj.AddComponent<SpriteRenderer>();
            
            Sprite sprite = Sprite.Create(
                canvas.Texture,
                new Rect(0, 0, canvas.Width, canvas.Height),
                new Vector2(0.5f, 0.5f),
                pixelPerfectManager.GetScaleFactor() * 16f
            );
            
            renderer.sprite = sprite;
            Debug.Log($"✓ Test sprite '{name}' created at {position}");
        }
        
        private void CompleteTest()
        {
            CancelInvoke(nameof(RunNextTestStep));
            testCompleted = true;
            testPassed = true;
            currentStatus = "All Tests Passed!";
            Debug.Log("🎉 All pixel rendering tests completed successfully!");
        }
        
        private void FailTest(string errorMessage)
        {
            CancelInvoke(nameof(RunNextTestStep));
            testCompleted = true;
            testPassed = false;
            currentStatus = $"Test Failed: {errorMessage}";
            Debug.LogError($"❌ Pixel rendering test failed: {errorMessage}");
        }
        
        [ContextMenu("Clear Test Results")]
        public void ClearTestResults()
        {
            GameObject[] testObjects = FindObjectsOfType<GameObject>();
            int cleared = 0;
            
            foreach (GameObject obj in testObjects)
            {
                if (obj.name.StartsWith("Test_"))
                {
                    DestroyImmediate(obj);
                    cleared++;
                }
            }
            
            testCompleted = false;
            testPassed = false;
            currentStatus = "Cleared";
            testStep = 0;
            
            Debug.Log($"Cleared {cleared} test objects");
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 120));
            
            GUILayout.Label("Simple Pixel Test", GUI.skin.box);
            GUILayout.Label($"Status: {currentStatus}");
            GUILayout.Label($"Step: {testStep}/5");
            GUILayout.Label($"Completed: {testCompleted}");
            GUILayout.Label($"Passed: {testPassed}");
            
            if (GUILayout.Button("Start Test"))
            {
                StartTest();
            }
            
            if (GUILayout.Button("Clear Results"))
            {
                ClearTestResults();
            }
            
            GUILayout.EndArea();
        }
    }
}
